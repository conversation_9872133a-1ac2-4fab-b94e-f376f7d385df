header {
  padding-top: @vw10;
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 99;
  .col {
    display: inline-block;
    vertical-align: top;
    width: 25%;
    &:nth-child(2){
      text-align: center;
      width: 50%;
      a {
        cursor: pointer;
      }
      .logo {
        cursor: pointer;
        width: auto;
        height: @vw80;
      }
    }
    &:last-child {
      text-align: right;
    }
  }
}

#menu {
  display: inline-block;
  padding: @vw20;
  cursor: pointer;
  position: absolute;
  left: 0;
  top: 0;
  color: @hardWhite;
  font-size: @vw21;
  z-index: 99;
  .transition(.3s);
  .background {
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
    border-radius: @vw20;
    background: @yellow;
    // transition: all 0.6s cubic-bezier(0.83, 0, 0.17, 1);
  }
  &:hover {
    // transform: skew(-3deg);
    .background {
      box-shadow: 0 0 @vw20 rgba(0,0,0,.2);
    }
  }
  &.active {
    .innerContent {
      display: block;
      width: (@vw99 * 4) + (@vw22 * 3);
    }
  }
  div, span {
    cursor: pointer;
    display: inline-block;
  }
  .innerContent {
    display: none;
    padding: @vw40 0;
    &.showContent {
      ul {
        &.hover {
          li {
            a {
              opacity: .2;
            }
            &.active {
              a {
                opacity: 1;
              }
            }
          }
        }
        li {
          visibility: visible;
          opacity: 1;
          transform: translateY(0);
          &:nth-child(2) {
            transition-delay: .15s;
          }
          &:nth-child(3) {
            transition-delay: .3s;
          }
          &:nth-child(4) {
            transition-delay: .45s;
          }
          &:nth-child(5) {
            transition-delay: .6s;
          }
          &:nth-child(6) {
            transition-delay: .75s;
          }
        }
      }
    }
    > div {
      display: block;
    }
    .menu-primary-menu-container {
      padding-bottom: @vw20;
      ul {
        li {
          font-size: @vw32;
          padding-bottom: @vw22;
          line-height: @vw32;
        }
      }
    }
    .menu-secondary-menu-container {
      ul {
        li {
          a {
            &:hover {
              opacity: .4;
            }
          }
        }
      }
    }
    ul {
      list-style: none;
      li {
        font-family: 'filson-pro', arial, sans-serif;
        font-size: @vw21;
        font-weight: 600;
        padding-bottom: @vw10;
        line-height: @vw31;
        text-transform: uppercase;
        visibility: hidden;
        position: relative;
        opacity: 0;
        transform: translateY(@vw20);
        .transition(.3s);
        a {
          cursor: pointer;
          color: @hardWhite;
          text-decoration: none;
          .transition(.15s);
        }
      }
    }
  }
  .hamburger {
    cursor: pointer;
    margin-left: @vw14;
    display: inline-block;
    height: @vw14;
    width: @vw16;
    .border {
      position: absolute;
      display: block;
      height: 2px;
      width: 100%;
      border-radius: @vw2;
      background: @hardWhite;
      &:first-child {
        top: 0;
      }
      &:nth-child(2) {
        top: 50%;
        transform: translateY(-50%);
      }
      &:nth-child(3) {
        bottom: 0;
        top: auto;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  header {
    padding-top: @vw10-1160;
    .col {
      &:nth-child(2){
        .logo {
          height: @vw80-1160;
        }
      }
    }
  }

  #menu {
    padding: @vw20-1160;
    font-size: @vw21-1160;
    .background {
      border-radius: @vw20-1160;
    }
    &:hover {
      .background {
        box-shadow: 0 0 @vw20-1160 rgba(0,0,0,.2);
      }
    }
    &.active {
      .innerContent {
        width: (@vw99-1160 * 4) + (@vw22-1160 * 3);
      }
    }
    .innerContent {
      padding: @vw40-1160 0;
      .menu-primary-menu-container {
        padding-bottom: @vw20-1160;
        ul {
          li {
            font-size: @vw32-1160;
            padding-bottom: @vw22-1160;
            line-height: @vw32-1160;
          }
        }
      }
      ul {
        li {
          font-size: @vw21-1160;
          padding-bottom: @vw10-1160;
          line-height: @vw31-1160;
          transform: translateY(@vw20-1160);
        }
      }
    }
    .hamburger {
      margin-left: @vw14-1160;
      height: @vw14-1160;
      width: @vw16-1160;
      .border {
        border-radius: @vw2-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  header {
    padding-top: @vw10-580;
    .col {
      width: 30%;
      &:nth-child(2){
        width: 40%;
        .logo {
          padding-top: @vw10-580;
          height: @vw60-580;
        }
      }
    }
  }

  #menu {
    padding: @vw20-580;
    font-size: @vw21-580;
    .background {
      border-radius: @vw20-580;
    }
    &:hover {
      .background {
        box-shadow: 0 0 @vw20-580 rgba(0,0,0,.2);
      }
    }
    &.active {
      .innerContent {
        width: (@vw99-580 * 4) + (@vw22-580 * 3);
      }
    }
    .innerContent {
      padding: @vw40-580 0;
      .menu-primary-menu-container {
        padding-bottom: @vw20-580;
        ul {
          li {
            font-size: @vw32-580;
            padding-bottom: @vw22-580;
            line-height: @vw32-580;
          }
        }
      }
      ul {
        li {
          font-size: @vw21-580;
          padding-bottom: @vw22-580;
          line-height: @vw32-580;
          transform: translateY(@vw20-580);
        }
      }
    }
    .hamburger {
      margin-left: @vw14-580;
      height: @vw14-580;
      width: @vw16-580;
      .border {
        border-radius: @vw2-580;
      }
    }
  }
}
