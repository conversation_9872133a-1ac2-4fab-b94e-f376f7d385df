footer {
  color: @hardWhite;
  background: @almostBlack;
  padding: @vw100 0;
  padding-bottom: @vw50;
  .topFooter {
    padding-bottom: @vw50;
  }
  .col {
    display: inline-block;
    vertical-align: top;
    .textTitle {
      margin-bottom: @vw20;
      opacity: .5;
    }
    ul {
      list-style: none;
      li {
        font-size: @vw21;
        a {
          color: @hardWhite;
          cursor: pointer;
          text-decoration: none;
          .transition(.3s);
          &:hover {
            opacity: .5;
          }
        }
      }
    }
    &:nth-child(1) {
      width: (@vw99 * 6) + (@vw22 * 6);
      padding-right: @vw99 + @vw22;
      ul {
        li {
          font-family: 'filson-pro', arial, sans-serif;
          font-size: @vw60;
          font-weight: 600;
          line-height: @vw60;
          text-transform: uppercase;
          margin-bottom: @vw10;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    &:nth-child(2), &:nth-child(3) {
      ul {
        li {
          margin-bottom: @vw5;
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
    &:nth-child(2) {
      width: (@vw99 * 3) + (@vw22 * 3);
    }
    &:nth-child(3) {
      width: (@vw99 * 2) + (@vw22 * 2);
    }
  }
  .signature {
    opacity: .5;
    font-size: @vw21;
  }
  .roundButton {
    transform: rotate(180deg);
  }
}

@media all and (max-width: 1160px) {
  footer {
    padding: @vw100-1160 0;
    padding-bottom: @vw50-1160;
    .topFooter {
      padding-bottom: @vw50-1160;
    }
    .col {
      .textTitle {
        margin-bottom: @vw20-1160;
      }
      ul {
        li {
          font-size: @vw21-1160;
        }
      }
      &:nth-child(1) {
        width: (@vw99-1160 * 4) + (@vw22-1160 * 3);
        padding-right: @vw99-1160 + @vw22-1160;
        ul {
          li {
            font-size: @vw40-1160;
            line-height: @vw40-1160;
            margin-bottom: @vw10-1160;
          }
        }
      }
      &:nth-child(2), &:nth-child(3) {
        ul {
          li {
            margin-bottom: @vw5-1160;
          }
        }
      }
      &:nth-child(2) {
        width: (@vw99-1160 * 2) + (@vw22-1160 * 2);
      }
      &:nth-child(3) {
        width: (@vw99-1160 * 1) + (@vw22-1160 * 1);
      }
    }
    .signature {
      font-size: @vw21-1160;
    }
  }
}

@media all and (max-width: 580px) {
  footer {
    padding: @vw100-580 0;
    padding-bottom: @vw50-580;
    .topFooter {
      padding-bottom: @vw50-580;
    }
    .col {
      margin-bottom: @vw40-580;
      .textTitle {
        margin-bottom: @vw20-580;
      }
      &:last-child {
        margin-bottom :0;
      }
      ul {
        li {
          font-size: @vw21-580;
        }
      }
      &:nth-child(1) {
        width: (@vw99-580 * 4) + (@vw22-580 * 3);
        padding-right: @vw99-580 + @vw22-580;
        ul {
          li {
            font-size: @vw40-580;
            line-height: @vw40-580;
            margin-bottom: @vw10-580;
          }
        }
      }
      &:nth-child(2), &:nth-child(3) {
        ul {
          li {
            margin-bottom: @vw5-580;
          }
        }
      }
      &:nth-child(2) {
        width: (@vw99-580 * 2) + (@vw22-580 * 1);
      }
      &:nth-child(3) {
        width: (@vw99-580 * 2) + (@vw22-580 * 1);
      }
    }
    .signature {
      font-size: @vw21-580;
    }
  }
}
