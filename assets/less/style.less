// out: ../../style.css, compress: true, strictMath: true

/*
Theme Name: LUBAV LTD
Author: <PERSON>
Version: 1.0.0
*/

@import 'constants.less';
@import 'default.less';
@import 'parts/header.less';
@import 'parts/footer.less';
@import 'parts/loader.less';
@import 'parts/ddsignature.less';

// blocks 
@import '../../blocks/less/block-big-header-block.less';
@import '../../blocks/less/block-faq-overview-block.less';
@import '../../blocks/less/block-header-checklist-block.less';
@import '../../blocks/less/block-header-two-column-block.less';
@import '../../blocks/less/block-image-text-block.less';
@import '../../blocks/less/block-instagram-block.less';
@import '../../blocks/less/block-latest-news-block.less';
@import '../../blocks/less/block-news-overview-block.less';
@import '../../blocks/less/block-programma-overview-block.less';
@import '../../blocks/less/block-sponsor-slider-block.less';
@import '../../blocks/less/block-sponsors-by-category-overview-block.less';
@import '../../blocks/less/block-youtube-videos-block.less';
@import '../../blocks/less/block-divider-block.less';
@import '../../blocks/less/block-image-links-block.less';
@import '../../blocks/less/block-programma-slider-block.less';
@import '../../blocks/less/block-news-header-block.less';
@import '../../blocks/less/block-two-images-block.less';
@import '../../blocks/less/block-text-two-column-block.less';
@import '../../blocks/less/block-image-bigger-block.less';
@import '../../blocks/less/block-image-gallery-block.less';
@import '../../blocks/less/block-contact-block.less';
@import '../../blocks/less/block-related-artiesten-block.less';

@font-face {
  font-family: 'Filson Pro';
  src:  url('assets/fonts/Filson Pro.woff2') format('woff2'),
        url('assets/fonts/Filson Pro.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

@font-face {
  font-family: 'icomoon';
  src:  url('assets/fonts/icomoon.eot?qwq3hi');
  src:  url('assets/fonts/icomoon.eot?qwq3hi#iefix') format('embedded-opentype'),
    url('assets/fonts/icomoon.ttf?qwq3hi') format('truetype'),
    url('assets/fonts/icomoon.woff?qwq3hi') format('woff'),
    url('assets/fonts/icomoon.svg?qwq3hi#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-ticket:before {
  content: "\e900";
}
.icon-arrow-down:before {
  content: "\e901";
}
.icon-location:before {
  content: "\e902";
}
.icon-arrow-right:before {
  content: "\e903";
}

::-webkit-scrollbar {
  width: @vw10;
}

::-webkit-scrollbar-track {
  background: @almostWhite;
}

::-webkit-scrollbar-thumb {
  border-radius: @vw50;
  background: rgba(0,0,0,.1);
}

.block__headline {
    padding: 20px 15px 30px;
    background: #fafafa;
    text-align: center;
}
.block__headline-title {
    font-family: 'Arial', sans-serif;
    font-size: 30px;
    font-weight: bold;
    position: relative;
}
.block__headline-title:after {
    content: '';
    display: block;
    width: 40px;
    height: 2px;
    background: #333;
    margin: 0 auto;
}

html.has-scroll-smooth {
	backface-visibility: hidden;
	transform: translateZ(0);
  [data-load-container] {
  	position: fixed;
  	top: 0;
  	right: 0;
  	bottom: 0;
  	left: 0;
  	width: 100vw;
  }
}

// Swup

.transition-fade {
  transition: .75s;
  opacity: 1;
}

html.is-animating .transition-fade {
  opacity: 0;
}

@media all and (max-width: 1160px) {

}

@media all and (max-width: 580px) {

}
