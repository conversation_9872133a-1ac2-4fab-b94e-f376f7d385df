* {
  box-sizing: border-box;
  cursor:default;
  letter-spacing: 0;
  margin:0;
  padding:0;
  position:relative;
  &::selection {
    background: @primaryColor;
    color: @hardWhite;
  }
  &::-webkit-selection {
    background: @primaryColor;
    color: @hardWhite;
  }
}

html.lenis {
  height: auto;
}

.lenis.lenis-smooth {
  scroll-behavior: auto;
}

.lenis.lenis-smooth [data-lenis-prevent] {
  overscroll-behavior: contain;
}

.lenis.lenis-stopped {
  overflow: hidden;
}

.lenis.lenis-scrolling iframe {
  pointer-events: none;
}
html {
  overflow-x: hidden;
}
body {
  background: @hardWhite;
  color: @grey;
  font-family: 'filson-pro', arial, sans-serif;
  overflow-x: hidden;
  font-size: @vw21;
  line-height: @vw31;
  strong {
    font-style: italic;
    font-weight: 600;
  }
  p {
    font-family: 'filson-pro', arial, sans-serif;
    font-size: @vw21;
    color: @hardBlack;
    font-weight: 100;
    line-height: @vw31;
    a {
      color: @pink;
      cursor: pointer;
      text-decoration: none;
      font-weight: 700;
      .transition(.15s);
      &:hover {
        opacity: .6;
      }
    }
  }
}

[data-scroll-section] {
  background: @hardWhite;
  // padding: @vw100 + @vw30 + @vw5 0 0 0;
}

[data-scroll-container] {
  position: absolute;
  top: 0;
  width: 100%;
}

section {
  margin: @vw100 0;
  &:first-child {
    margin-top: 0;
    padding-top: @vw100;
  }
  &.yellow, &.pink, &.blue, &.green {
    padding: @vw80 + @vw40 0;
    color: @hardWhite;
    overflow: hidden;
    border-radius:  @vw32;
    &.noBorder {
      border-radius: 0;
    }
    &.noborderBottom {
      border-radius: @vw32 @vw32 0 0;
    }
    &:first-child {
      border-radius: 0;
    }
  }
  &.yellow {
    background: @yellow;
  }
  &.pink {
    background: @pink;
  }
  &.blue {
    background: @blue;
  }
  &.green {
    background: @green;
  }
  &.noMarginTop {
    margin-top: 0;
  }
  &.noMarginBottom {
    margin-bottom: 0;
  }
}

.contentWrapper {
  display: block;
  // width: @vw1474;
  width: 100vw;
  padding: 0 (@vw99) + (@vw22 * 1);
  margin: auto;
  &.small {
    padding: 0 (@vw99 * 2) + (@vw22 * 3);
  }
  &.smaller {
    padding: 0 (@vw99 * 3) + (@vw22 * 4);
  }
}

.hugeTitle, .bigTitle, .mediumTitle {
  .secondary {
    color: @secondaryColor;
  }
  &.white {
    color: @hardWhite;
  }
  &.upper {
    text-transform: uppercase;
  }
}

.hugeTitle {
  font-family: 'Unbounded', arial, sans-serif;
  font-size: @fsHuge;
  font-weight: 400;
  line-height: @lhHuge;
}

.boldText {
  color: @secondaryColorLight;
  font-family: 'Unbounded', arial, sans-serif;
  font-size: @vw50 + @vw5;
  font-weight: 400;
  line-height: @vw50 + @vw5;
}

.subTitle {
  font-family: 'filson-pro', arial, sans-serif;
  display: inline-block;
  border-radius: @vw40;
  padding: @vw10 @vw20;
  font-weight: 200;
  color: @lightGrey;
  background: @backgroundGrey;
  font-size: @vw21;
  line-height: @vw31;
}

.bigTitle {
  font-family: 'filson-pro', arial, sans-serif;
  font-size: @vw60;
  font-weight: 600;
  color: @hardBlack;
  line-height: @vw60;
  &.splitThis {
    .wrapper {
      height: @lhBig + @vw10 + @vw5;
    }
  }
}

.normalTitle {
  color: @hardBlack;
  font-family: 'filson-pro', arial, sans-serif;
  font-size: @vw36;
  font-weight: 600;
  line-height: @vw36;
}

.title, .linkTitle {
  font-family: 'filson-pro', arial, sans-serif;
  font-size: @vw40;
  font-weight: 200;
  line-height: @vw40;
}

.bigTitle, .mediumTitle {
  &.splitThis {
    &.animate {
      .wrapper {
        &:nth-child(1) {
          .innerWrapper {
            transition-delay: .15s;
          }
        }
        &:nth-child(2) {
          .innerWrapper {
            transition-delay: .3s;
          }
        }
        &:nth-child(3) {
          .innerWrapper {
            transition-delay: .45s;
          }
        }
        &:nth-child(4) {
          .innerWrapper {
            transition-delay: .6s;
          }
        }
        &:nth-child(5) {
          .innerWrapper {
            transition-delay: .75s;
          }
        }
        &:nth-child(6) {
          .innerWrapper {
            transition-delay: .9s;
          }
        }
        &:nth-child(7) {
          .innerWrapper {
            transition-delay: 1.2s;
          }
        }
        .innerWrapper {
          opacity: 1;
          transition: opacity .15s ease-in, transform 0.6s cubic-bezier(0.16, 1, 0.3, 1);
          transform: translateY(0) translateZ(0);
        }
      }
    }
    .wrapper {
      position: relative;
      overflow: hidden;
      .innerWrapper {
        position: absolute;
        top: 0;
        opacity: 0;
        transform: translateY(100%) translateZ(0);
      }
    }
  }
}

.textTitle {
  font-family: 'filson-pro', arial, sans-serif;
  font-size: @vw21;
  font-weight: 600;
  line-height: @vw31;
  &.thin {
    font-weight: 400;
  }
}


.smallText {
  font-family: 'filson-pro', arial, sans-serif;
  font-size: @vw12;
  font-weight: 200;
  line-height: @vw21;
}

.roundButton {
  cursor: pointer;
  display: inline-block;
  border: 1px solid @hardWhite;
  color: @hardWhite;
  text-align: center;
  line-height: @vw80;
  opacity: .5;
  height: @vw80;
  width: @vw80;
  .transition(.3s);
  border-radius: 50%;
  &:hover {
    opacity: 1;
  }
  i {
    cursor: pointer;
    line-height: @vw80;
    font-size: @vw20;
  }
}

.button {
  display: inline-block;
  border: 1px solid @blue;
  color: @blue;
  padding: @vw20;
  font-size: @vw21;
  height: auto;
  width: auto;
  border-radius: @vw20;
  cursor: pointer;
  overflow: hidden;
  &.pink {
    background: @pink;
    border-color: @pink;
    color: @hardWhite;
    &.white {
      &:hover {
        border-color: @hardWhite;
      }
    }
    &:hover {
      background: transparent;
      border-color: @hardWhite;
      box-shadow: 0 0 @vw10 rgba(0,0,0,.1);
      .innerText {
        &:first-child {
          color: @pink;
        }
      }
      &:before {
        background: @hardWhite;
        z-index: 1;
      }
      .innerText {
        z-index: 2;
      }
    }
  }
  &.yellow {
    background: @yellow;
    border-color: @yellow;
    color: @hardWhite;
    &.white {
      &:hover {
        border-color: @hardWhite;
      }
    }
    &:hover {
      background: transparent;
      border-color: @hardWhite;
      box-shadow: 0 0 @vw10 rgba(0,0,0,.1);
      .innerText {
        &:first-child {
          color: @yellow;
        }
      }
      &:before {
        background: @hardWhite;
        z-index: 1;
      }
      .innerText {
        z-index: 2;
      }
    }
  }
  &.blue {
    &.white {
      &:hover {
        border-color: @hardWhite;
        .innerText {
          &:first-child {
            color: @blue;
          }
        }
        &:before {
          background: @hardWhite;
          z-index: 1;
        }
        .innerText {
          z-index: 2;
        }
      }
    }
    &.fill {
      background: @blue;
      color: @hardWhite;
      &:before {
        background: @blue;
      }
      &:hover {
        // background: transparent;
        color: @blue;
        &:before {
          background: @hardWhite;
          z-index: 1;
        }
        .innerText {
          z-index: 2;
        }
      }
    }
    &:hover {
      color: @hardWhite;
    }
  }
  &:hover {
      color: @pink;
      border-color: @blue;
      &.white {
        border-color: @blue;
      }
      &:before {
        transition: all 0.6s cubic-bezier(0.76, 0, 0.24, 1);
        background: @blue;
        transform: translateY(-50%) translateX(0);
        width: 100%;
        opacity: 1;
      }
      .innerText {
        span {
          &:not(:first-child) {
            opacity: 0;
            transition-delay: 0s;
            transform: translateY(-50%) translateX(@vw10);
            &:last-child {
              transition-delay: .15s;
              opacity: 1;
              transform: translateY(-50%) translateX(0);
            }
          }
        }
      }
    }
    &:before {
      .transition(.3s);
      content: '';
      background: @hardWhite;
      position: absolute;
      border-radius: @vw20;
      padding-bottom: 100%;
      left: 0;
      opacity: 0;
      top: 50%;
      transform: translateY(-50%) translateX(-100%);
      width: @vw20;
      height: 0;
      z-index: -1;
    }
    .innerText {
      display: block;
      pointer-events: none;
      span {
        pointer-events: none;
        &:first-child {
          display: block;
          opacity: 0;
        }
        &:not(:first-child) {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 0;
          right: 0;
          margin: auto;
          .transition(.3s);
          transition-delay: .15s;
          &:last-child {
            opacity: 0;
            transition-delay: 0s;
            transform: translateY(-50%) translateX(-@vw20);
          }
        }
      }
    }
  &.white {
    border-color: @hardWhite;
    color: @hardWhite;
  }
  span {
    display: inline-block;
    vertical-align: middle;
  }
  i {
    display: inline-block;
    vertical-align: middle;
    margin-left: @vw14;
    font-size: @vw14;
  }
}

.textLink {
  color: @hardBlack;
  display: inline-block;
  font-family: 'filson-pro', arial, sans-serif;
  font-size: @fsDefault;
  font-weight: 500;
  font-style: italic;
  text-decoration: none;
  line-height: @lhDefault;
  .transition(.3s);
  &:hover {
    color: @secondaryColor;
    span {
      &:before {
        width: 0;
      }
      &:after {
        width: 100%;
        transition-delay: .15s;
      }
    }
    i {
      transform: translateY(@vw1 + @vw1) translateZ(0);
    }
  }
  span {
    position: relative;
    &:before, &:after{
      content: '';
      position: absolute;
      background: @hardBlack;
      width: 100%;
      height: 1px;
      bottom: 0;
      right: 0;
      .transition(.3s);
    }
    &:after {
      background: @secondaryColor;
      width: 0;
      right: auto;
      left: 0;
    }
  }
  i {
    transform: translateY(0) translateZ(0);
    .transition(.15s);
  }
  > i, > span {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
  }
}

@media all and (max-width: 1160px) {
  body {
    font-size: @vw21-1160;
    line-height: @vw31-1160;
    p {
      font-size: @vw21-1160;
      line-height: @vw31-1160;
    }
  }

  section {
    margin: @vw100-1160 0;
    &:first-child {
      padding-top: @vw100-1160;
    }
    &.yellow, &.pink, &.blue, &.green {
      padding: @vw80-1160 + @vw40-1160 0;
      border-radius: @vw32-1160;
      &.noborderBottom {
        border-radius: @vw32-1160 @vw32-1160 0 0;
      }
    }
  }

  .contentWrapper {
    padding: 0 (@vw99-1160) + (@vw22-1160 * 1);
    &.small {
      padding: 0 (@vw99-1160) + (@vw22-1160 * 1);
    }
    &.smaller {
      padding: 0 (@vw99-1160 ) + (@vw22-1160 * 1);
    }
  }

  .subTitle {
    font-size: @vw21-1160;
    border-radius: @vw40-1160;
    padding: @vw10-1160 @vw20-1160;
    line-height: @vw31-1160;
  }

  .bigTitle {
    font-size: @vw60-1160;
    line-height: @vw60-1160;
  }

  .normalTitle {
    font-size: @vw36-1160;
    line-height: @vw36-1160;
  }

  .title, .linkTitle {
    font-size: @vw40-1160;
    line-height: @vw40-1160;
  }

  .textTitle {
    font-size: @vw21-1160;
    line-height: @vw31-1160;
  }

  .smallText {
    font-size: @vw12-1160;
    line-height: @vw21-1160;
  }

  .roundButton {
    line-height: @vw80-1160;
    height: @vw80-1160;
    width: @vw80-1160;
    i {
      line-height: @vw80-1160;
      font-size: @vw20-1160;
    }
  }

  .button {
    padding: @vw20-1160;
    font-size: @vw21-1160;
    border-radius: @vw20-1160;
    &.pink {
      &:hover {
        box-shadow: 0 0 @vw10-1160 rgba(0,0,0,.1);
      }
    }
    &.yellow {
      &:hover {
        box-shadow: 0 0 @vw10-1160 rgba(0,0,0,.1);
      }
    }
      &:before {
        border-radius: @vw20-1160;
        width: @vw20-1160;
      }
      .innerText {
        span {
          &:not(:first-child) {
            &:last-child {
              transform: translateY(-50%) translateX(-@vw20-1160);
            }
          }
        }
      }
    i {
      margin-left: @vw14-1160;
      font-size: @vw14-1160;
    }
  }

  .textLink {
    font-size: @vw21-1160;
    line-height: @vw32-1160;
    &:hover {
      i {
        transform: translateY(@vw1-1160 + @vw1-1160) translateZ(0);
      }
    }
  }
}

@media all and (max-width: 580px) {
  body {
    font-size: @vw21-580;
    line-height: @vw31-580;
    p {
      font-size: @vw21-580;
      line-height: @vw31-580;
    }
  }

  section {
    margin: @vw100-580 0;
    &:first-child {
      padding-top: @vw100-580;
    }
    &.yellow, &.pink, &.blue, &.green {
      padding: @vw80-580 + @vw40-580 0;
      border-radius: @vw32-580;
      &.noborderBottom {
        border-radius: @vw32-580 @vw32-580 0 0;
      }
    }
  }

  .contentWrapper {
    padding: 0 @vw22-580;
    &.small {
      padding: 0 @vw22-580;
    }
    &.smaller {
      padding: 0 @vw22-580;
    }
  }

  .subTitle {
    font-size: @vw21-580;
    border-radius: @vw40-580;
    padding: @vw10-580 @vw20-580;
    line-height: @vw31-580;
  }

  .bigTitle {
    font-size: @vw50-580;
    line-height: @vw50-580;
  }

  .normalTitle {
    font-size: @vw36-580;
    line-height: @vw36-580;
  }

  .title, .linkTitle {
    font-size: @vw40-580;
    line-height: @vw40-580;
  }

  .textTitle {
    font-size: @vw21-580;
    line-height: @vw31-580;
  }

  .smallText {
    font-size: @vw12-580;
    line-height: @vw21-580;
  }

  .roundButton {
    line-height: @vw80-580;
    height: @vw80-580;
    width: @vw80-580;
    i {
      line-height: @vw80-580;
      font-size: @vw20-580;
    }
  }

  .button {
    padding: @vw20-580;
    font-size: @vw21-580;
    border-radius: @vw20-580;
    &.pink {
      &:hover {
        box-shadow: 0 0 @vw10-580 rgba(0,0,0,.1);
      }
    }
    &.yellow {
      &:hover {
        box-shadow: 0 0 @vw10-580 rgba(0,0,0,.1);
      }
    }
      &:before {
        border-radius: @vw20-580;
        width: @vw20-580;
      }
      .innerText {
        span {
          &:not(:first-child) {
            &:last-child {
              transform: translateY(-50%) translateX(-@vw20-580);
            }
          }
        }
      }
    i {
      margin-left: @vw14-580;
      font-size: @vw14-580;
    }
  }

  .textLink {
    font-size: @vw21-580;
    line-height: @vw32-580;
    &:hover {
      i {
        transform: translateY(@vw1-580 + @vw1-580) translateZ(0);
      }
    }
  }
}
