var menuIsOpen = false;
var defaultMenuWidth;
var defaultMenuHeight;

var container;
var oldHeight;
var newHeight;

var menuDisabled = false;

document.fonts.ready.then(function(){
  gsap.registerPlugin(CustomEase);
  CustomEase.create(
    "menubackground",
    "M0,0 C0.83,0 0.17,1 1,1"
  );

  defaultMenuWidth = $("header #menu").outerWidth();
  defaultMenuHeight = $("header #menu").outerHeight();


  gsap.to("header #menu .background", 0, {
    width: defaultMenuWidth,
    height: defaultMenuHeight
  });

  $(window).on("resize", function(){
    defaultMenuWidth = $("header #menu").outerWidth();
    defaultMenuHeight = $("header #menu").outerHeight();

    gsap.to("header #menu .background", 0, {
      width: $("header #menu").outerWidth(),
      height: $("header #menu").outerHeight()
    });
  });

  $(document).on("click", "header #menu", function(e){
    if (!menuDisabled) {
      menuDisabled = true;
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu(e.currentTarget);
      } else {
        openMenu(e.currentTarget);
        menuIsOpen = true;
      }
    }
  });


  setTimeout(function(){
    scroller.on("scroll", function(){
      if (!menuDisabled) {
        if (menuIsOpen) {
          menuIsOpen = false;
          closeMenu("header #menu");
        }
      }
    });
  }, 400);

  $(document).on("click", function(e){
    if (!menuDisabled) {
      if (menuIsOpen) {
        menuIsOpen = false;
        closeMenu("header #menu");
      }
    }
  });

  $(document).on("mouseover", "header #menu .menu-primary-menu-container li", function(e){
    var item = $(e)[0].currentTarget
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").addClass("hover");
    $(item).addClass("active");
  });

  $(document).on("mouseleave", "header #menu .menu-primary-menu-container li", function(e){
    var item = $(e)[0].currentTarget
    $(item).parents("ul").find("li").removeClass("active");
    $(item).parents("ul").removeClass("hover");
  });
});

function openMenu(menu) {
  $(menu).toggleClass("active");
  gsap.to($(menu).find(".background"), .9, {
    width: $(menu).outerWidth(),
    height: $(menu).outerHeight(),
    ease: "menubackground"
  });
  scroller.stop();
  setTimeout(function(){
    $(menu).find(".innerContent").addClass("showContent");
  }, 600);
  setTimeout(function(){
    menuDisabled = false;
  }, 900);
}

function closeMenu(menu) {
  scroller.start();
  $(menu).find(".innerContent").removeClass("showContent");
  setTimeout(function(){
    gsap.to($(menu).find(".background"), .3, {
      width: defaultMenuWidth,
      height: defaultMenuHeight,
      ease: "menubackground"
    });
    $(menu).toggleClass("active");
  }, 600);
  setTimeout(function(){
    menuDisabled = false;
  }, 900);
}
