var pageContainerWrap;
var scroller;
var scrollerHeight = 0;
var currentScrollY = 0;
var scrollValues = {};
var dynamicScripts = [];
var popState = false;
var resizeFunction;
var inlineStyles = null;

document.fonts.ready.then(function(){
  gsap.registerPlugin(ScrollTrigger)


  if ('scrollRestoration' in history) {
    history.scrollRestoration = 'manual';
  }

  updateDynamicScriptsArray();


  pageContainerWrap = new Swup({
    cache:true,
    containers: ["#pageContainer"],
    animateHistoryBrowsing: true,
    plugins: [new SwupHeadPlugin({
      persistAssets: true,
      persistTags: 'style link',
    }), new SwupGtagPlugin({
      gaMeasurementId: '',
    })]
  });

  pageContainerWrap.on('clickLink', () => {
    scrollValues[window.location.href] = window.scrollY;
  });

  pageContainerWrap.on('popState', () => {
    popState = true;
    $(document).on("initPage", function(){
      if(popState){
        window.scrollTo(0, scrollValues[window.location.href]);
        popState = false;
      }
    });
    setFooter();
  });

  containerWidth = $(window).width();

  setTimeout(function(){
    preloadPage();
  }, 100);

  pageContainerWrap.on('willReplaceContent', () => {
    //Store the inline styles from th head, so they can be put back when in pageView event
    inlineStyles = $("head style");
  });


  pageContainerWrap.on('pageView', () => {
    dynamicScriptLoad();
    updateDynamicScriptsArray();
    if (inlineStyles) {
      $("head").append($(inlineStyles));
      inlineStyles = null;
    }
    setTimeout(function(){
      initPage();
    }, 100);
  });

  pageContainerWrap.on('animationOutDone', () => {
    scroller.scrollTo(0,{offset: 0, duration:0, easing: "linear", immediate: true});
    $("header").removeClass("scrolled scrollDown");
    scroller.stop();
    scroller.start();
    $("html").addClass("stopScroll");
  });

  pageContainerWrap.on('willReplaceContent', () => {
    $("html").addClass("stopScroll");
  });
});
function updateDynamicScriptsArray(){
  $("head script").each(function(i, el){
    if($.inArray($(el).attr("src"), dynamicScripts) == -1){
      dynamicScripts.push($(el).attr("src"));
    }
  });
}

function dynamicScriptLoad(){
  $("head script").each(function(i, el){
    if($.inArray($(el).attr("src"), dynamicScripts) == -1){
      let scriptEle = document.createElement("script");
      scriptEle.setAttribute("src", $(el).attr("src"));
      $(el).remove();
      document.head.appendChild(scriptEle);
    }
  });
  var container = $("#pageContainer")[0];
  var arr = container.getElementsByTagName('script');
  // for (var n = 0; n < arr.length; n++){
  //   eval(arr[n].innerHTML);
  // }
}

function initLenis(){
  scroller = new Lenis({
    duration: 1.2,
    easing: (t) => Math.min(1, 1.001 - Math.pow(2, -10 * t)), // https://www.desmos.com/calculator/brs54l4xou
    orientation: 'vertical', // vertical, horizontal
    gestureOrientation: 'vertical', // vertical, horizontal, both
    smoothWheel: true,
    smoothTouch: false,
  });
  scroller.on("scroll", function(e){
    if(currentScrollY > 0){
      $("header").addClass("scrolled");
    } else {
      $("header").removeClass("scrolled");
    }
    // console.log(e.direction);
    if(e.direction == 1){
      $("header").addClass("scrollDown");
    } else if(e.direction == -1) {
      $("header").removeClass("scrollDown");
    }
    // if (!menuDisabled && menuIsOpen) {
    //   closeMenu($("header #menu"));
    // }
    var setHeaderColorDark = false;
    $(".changeHeaderToDark").each(function(i,el){
      if($(el).offset().top - $("header .innerBar .right a").offset().top - ($("header .innerBar .right a").height() / 2) <= 0 && $(el).offset().top - $("header .innerBar .right a").offset().top - ($("header .innerBar .right a").height() / 2) >= -Math.abs($(el).outerHeight())){
        setHeaderColorDark = true;
      }
    });
    if(setHeaderColorDark){
      $("header").addClass("dark");
    } else {
      $("header").removeClass("dark");
    }
  });
}

function raf(time) {
  scroller.raf(time);
  ScrollTrigger.update();
  requestAnimationFrame(raf);
}

function preloadPage(){
  initLenis();
  initPage();
  currentScrollY = $(window).scrollTop();
  scroller.on("scroll", function(e){
    currentScrollY = $(window).scrollTop();
  });

  requestAnimationFrame(raf);

  setTimeout(function(){
    $("html, body").removeClass("overflow");
    $(".content").removeClass("fade");
    $("header").addClass("active");
  }, 300);

  // Initialize Contact Form 7 redirect functionality on first load
  initContactForm7Redirect();
}

function initPage(){

  $("html").removeClass("stopScroll fade");

  $(document).trigger("initPage");

  if ($(".instagramBlock").length > 0) {
    sbi_init();
    setTimeout(function(){
      sbi_init();
      console.log("sbi init");
    }, 400);
  }

  lazyLoadImages();

  setFooter();

  // Initialize Contact Form 7 redirect functionality
  initContactForm7Redirect();

}

function lazyLoadImages() {
  var lazyloadImages;
  if ("IntersectionObserver" in window) {
    lazyloadImages = document.querySelectorAll(".lazy, .lazyload");
    const config = {
      root: null, // avoiding 'root' or setting it to 'null' sets it to default value: viewport
      rootMargin: '1000px',
      threshold: 0.0
    };
    var imageObserver = new IntersectionObserver(function(entries, observer) {
      $(entries).each(function(i, entry) {
        if (entry.isIntersecting) {
          var image = entry.target;
          image.classList.remove("lazy");
          image.src = $(image).data('src');
          imageObserver.unobserve(image);
        }
      });
    },config);
    $(lazyloadImages).each(function(i, image) {
      imageObserver.observe(image);
    });
  } else {
    $(".lazy").each(function(i, image) {
      image.classList.remove("lazy");
      image.src = $(image).data('src');
    });
  }
}

function setFooter() {
  $(document).on("click", "footer .roundButton", function(){
    scroller.scrollTo("#pageContainer");
  });
}

// Contact Form 7 redirect functionality
function initContactForm7Redirect() {
  // Listen for Contact Form 7 mail sent event
  document.addEventListener('wpcf7mailsent', function(event) {
    console.log('Contact Form 7 mail sent successfully, Form ID:', event.detail.contactFormId);

    // Check if this is a booking request form (check for form ID 6 or forms containing booking-related fields)
    var formElement = event.target;
    var isBookingForm = false;

    // Method 1: Check by form ID (based on your logs showing form ID 6)
    if (event.detail.contactFormId == 6) {
      isBookingForm = true;
    }

    // Method 2: Check if form contains booking-related fields as fallback
    if (!isBookingForm && formElement) {
      var bookingFields = formElement.querySelectorAll('input[name*="booking"], input[name*="artiest"], input[name*="event"], select[name*="city"]');
      if (bookingFields.length > 0) {
        isBookingForm = true;
      }
    }

    if (isBookingForm) {
      console.log('Booking request form submitted successfully, redirecting...');

      // Clear any cached form data
      if (typeof(Storage) !== "undefined") {
        localStorage.removeItem('booking_form_data');
        sessionStorage.removeItem('booking_form_data');
        localStorage.clear();
        sessionStorage.clear();
      }

      // Redirect to thank you page after a short delay
      setTimeout(function() {
        // Redirect to thank you page
        window.location.href = window.location.origin + '/thank-you/';
      }, 1500);
    }
  }, false);

  // Listen for Contact Form 7 submission failed event
  document.addEventListener('wpcf7mailfailed', function(event) {
    console.log('Contact Form 7 mail failed, Form ID:', event.detail.contactFormId);

    // Check if this is a booking form
    var formElement = event.target;
    var isBookingForm = event.detail.contactFormId == 6;
    if (!isBookingForm && formElement) {
      var bookingFields = formElement.querySelectorAll('input[name*="booking"], input[name*="artiest"], input[name*="event"], select[name*="city"]');
      if (bookingFields.length > 0) {
        isBookingForm = true;
      }
    }

    if (isBookingForm) {
      console.log('Booking request form submission failed');
      // Clear any cached form data on failure too
      if (typeof(Storage) !== "undefined") {
        localStorage.removeItem('booking_form_data');
        sessionStorage.removeItem('booking_form_data');
      }
    }
  }, false);

  // Listen for Contact Form 7 spam detected event
  document.addEventListener('wpcf7spam', function(event) {
    console.log('Contact Form 7 spam detected, Form ID:', event.detail.contactFormId);

    // Check if this is a booking form
    var formElement = event.target;
    var isBookingForm = event.detail.contactFormId == 6;
    if (!isBookingForm && formElement) {
      var bookingFields = formElement.querySelectorAll('input[name*="booking"], input[name*="artiest"], input[name*="event"], select[name*="city"]');
      if (bookingFields.length > 0) {
        isBookingForm = true;
      }
    }

    if (isBookingForm) {
      console.log('Booking request form marked as spam');
      // Clear any cached form data on spam detection too
      if (typeof(Storage) !== "undefined") {
        localStorage.removeItem('booking_form_data');
        sessionStorage.removeItem('booking_form_data');
      }
    }
  }, false);
}
