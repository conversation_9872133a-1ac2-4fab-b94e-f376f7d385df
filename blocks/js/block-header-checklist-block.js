document.fonts.ready.then(function(){

  if ($(".headerChecklistBlock").length > 0) {
    setHeaderChecklistBlock();
  }

  pageContainerWrap.on('pageView', () => {
    if ($(".headerChecklistBlock").length > 0) {
      setHeaderChecklistBlock();
    }
  });

});

function setHeaderChecklistBlock() {
  if ($(".headerChecklistBlock video").length > 0) {
    var video = $(".headerChecklistBlock video")[0];
    video.pause();
    video.load();
    video.play();
  }
  gsap.to('.headerChecklistBlock img', {
    scale: 1.3,
    rotate: 5 + "deg",
    ease: 'ease-in-out',
    z: 0,
    scrollTrigger : {
      trigger: ".headerChecklistBlock",
      start: "top top",
      end: 'bottom top',
      scrub: true,
    }
  });
  $(document).on("click", ".headerChecklistBlock .roundButton", function(){
    scroller.scrollTo("body section:nth-child(2)");
  });
  $(document).on("click", ".headerChecklistBlock .checkItem", function(){
    $(this).toggleClass("active");
  });
}
