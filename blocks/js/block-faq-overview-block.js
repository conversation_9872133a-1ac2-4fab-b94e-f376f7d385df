document.fonts.ready.then(function(){

  if ($(".faqOverviewBlock").length > 0) {
    setFaqBlock();
  }
  pageContainerWrap.on('pageView', () => {
    if ($(".faqOverviewBlock").length > 0) {
      setFaqBlock();
    }
  });
});

function setFaqBlock() {
  $(document).on("click", ".faqOverviewBlock .header", function(){
    $(this).parents(".items").find(".item").removeClass("open");
    $(this).parents(".items").find(".content").css("height", 0 + "px");
    $(this).parents(".item").addClass("open");
    $(this).parents(".item").find(".content").css("height", $(this).parents(".item").find(".innerContent").outerHeight());
  });
}
