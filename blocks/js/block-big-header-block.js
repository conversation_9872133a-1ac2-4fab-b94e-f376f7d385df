document.fonts.ready.then(function(){
  if ($(".bigHeaderBlock").length > 0) {
    setBigHeaderBlock();
  }

  pageContainerWrap.on('pageView', () => {
    if ($(".bigHeaderBlock").length > 0) {
      setBigHeaderBlock();
    }
  });
});

function setBigHeaderBlock() {
  var video = $("#headerVideo")[0];
  video.pause();
  video.load();
  video.play();
  gsap.to('.bigHeaderBlock video', {
    scale: 1.3,
    borderRadius: 0,
    ease: 'ease-in-out',
    z: 0,
    scrollTrigger : {
      trigger: ".bigHeaderBlock",
      start: "top top",
      end: 'bottom top',
      scrub: true,
    }
  });
  gsap.to('.bigHeaderBlock .animWrapper svg', {
    scale: .95,
    rotate: 5 + "deg",
    ease: 'ease-in-out',
    z: 0,
    scrollTrigger : {
      trigger: ".bigHeaderBlock",
      start: "top top",
      end: 'bottom top',
      scrub: true,
    }
  });
  $(document).on("click", ".bigHeaderBlock .roundButton", function(){
    scroller.scrollTo("body section:nth-child(2)");
  });
}
