document.fonts.ready.then(function(){
  if ($(".imageGalleryBlock").length > 0) {
    setImageGalleryBlock();
  }
  pageContainerWrap.on('pageView', () => {
    if ($(".imageGalleryBlock").length > 0) {
      setImageGalleryBlock();
    }
  });
});

function setImageGalleryBlock() {
  var counter = 1;
  $(".imageGalleryBlock .modula-item").each(function(i, el){
    $(el).find("a").attr("target", "_blank");
    if (counter == 1 || counter == 4) {
      $(el).addClass("small");
    }
    if (counter == 4) {
      counter = 0;
    }
    counter = counter + 1;
  });
}
