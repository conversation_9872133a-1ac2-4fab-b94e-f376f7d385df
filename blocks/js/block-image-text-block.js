document.fonts.ready.then(function(){
  if ($(".imageTextBlock").length > 0) {
    setImageTextBlock();
  }
  pageContainerWrap.on('pageView', () => {
    if ($(".imageTextBlock").length > 0) {
      setImageTextBlock();
    }
  });
});

function setImageTextBlock() {
  gsap.to('.imageTextBlock img', {
    scale: 1.1,
    rotate: 4 + "deg",
    ease: 'ease-in-out',
    z: 0,
    scrollTrigger : {
      trigger: ".imageTextBlock",
      start: "top bottom",
      end: 'bottom top',
      scrub: true,
    }
  });
}
