document.fonts.ready.then(function(){

  if ($(".headerTwoColumnBlock").length > 0) {
    setHeaderTwoColumnBlock();
  }
  pageContainerWrap.on('pageView', () => {
    if ($(".headerTwoColumnBlock").length > 0) {
      setHeaderTwoColumnBlock();
    }
  });

});

function setHeaderTwoColumnBlock() {
  if ($(".headerTwoColumnBlock video").length > 0) {
    var video = $(".headerTwoColumnBlock video")[0];
    video.pause();
    video.load();
    video.play();
  }
  gsap.to('.headerTwoColumnBlock img', {
    scale: 1.3,
    rotate: 5 + "deg",
    ease: 'ease-in-out',
    z: 0,
    scrollTrigger : {
      trigger: ".headerTwoColumnBlock",
      start: "top top",
      end: 'bottom top',
      scrub: true,
    }
  });
  $(document).on("click", ".headerTwoColumnBlock .roundButton", function(){
    scroller.scrollTo("body section:nth-child(2)");
  });
}
