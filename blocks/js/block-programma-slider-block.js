var slidesWidth;

document.fonts.ready.then(function(){

  if ($(".programmaSliderBlock").length > 0) {
    slidesWidth = 0;
    setProgrammaSliderBlock();
  }
  pageContainerWrap.on('pageView', () => {
    slidesWidth = 0;
    if ($(".programmaSliderBlock").length > 0) {
      setProgrammaSliderBlock();
    }
  });

});

function setProgrammaSliderBlock() {
  if ($(window).outerWidth() > 579) {
    $(".programmaSliderBlock .item:visible").each(function(i, el) {
      if (i > 0) {
        slidesWidth = slidesWidth + $(el).outerWidth(true);
      }
    });

    $(".programmaSliderBlock .contentWrapper").css("height", slidesWidth);
    gsap.to('.programmaSliderBlock .items', {
      ease: 'ease-in-out',
      z: 0,
      x: -slidesWidth,
      scrollTrigger : {
        trigger: '.programmaSliderBlock',
        start: 'top top',
        duration: '100%',
        pin: true,
        pinSpacing: false,
        scrub: true,
        end: "bottom bottom",
      }
    });
  }
}
