<section class="newsOverviewBlock">
  <div class="contentWrapper small">
    <?php if (block_value("subtitle")){ ?><div class="subTitle"><?php block_field("subtitle") ?></div><?php } ?>
    <h2 class="bigTitle"><?php block_field("title") ?></h2>
    <p class="text"><?php block_field("text") ?></p>
    <?php if(block_value("button-url")) { ?>
      <a href="<?php block_field("button-url") ?>" class="button blue"><span class="innerText"><span><?php block_field("button-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("button-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("button-label") ?><i class="icon-arrow-right"></i></span></span></a>
    <?php } ?>
    <div class="news">
      <?php
      $loop = new WP_Query( array(
        'category' => '2'
      ) );
       while ( $loop->have_posts() ) : $loop->the_post();
        echo("<a href='".get_page_link()."' class='newsItem'>");
        echo("<span class='innerCol'><h3 class='bigTitle'>");
        echo(get_the_date("Y")."</h3>");
        echo("<span class='textTitle thin'>".get_the_date("d/m/2023")."</span>");
        echo("</span><span class='innerCol'><span class='textTitle'>");
        echo(the_title());
        echo("</span><span class='text'>");
        echo(the_excerpt());
        echo("</span><i class='icon-arrow-right'></i></span></a>");
        endwhile; ?>
    </div>
  </div>
</section>
