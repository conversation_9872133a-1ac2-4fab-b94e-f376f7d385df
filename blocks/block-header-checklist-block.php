<section class="headerChecklistBlock<?php if (block_value("border-radius")) { ?> borderRadiusBottom<?php } ?> <?php block_field("background-color") ?><?php if (block_value("no-margin-bottom")) { ?> noMarginBottom<?php } ?>">
  <div class="background">
    <?php if (block_value("video-url")) { ?>
      <video poster="<?php block_field("image") ?>" class="video" muted playsinline loop autoplay>
          <source src="<?php block_field("video-url") ?>" type="video/mp4">
      </video>
    <?php } else { ?>
      <img class="image" src="<?php block_field("image") ?>" alt="<?php echo get_theme_mod('tmid-main-callout-title') ?> - <?php the_title(); ?>">
    <?php } ?>
    <div class="textWrapper">
      <div class="contentWrapper small">
        <h1 class="bigTitle"><?php block_field("title") ?></h1>
        <span class="roundButton"><i class="icon-arrow-down"></i></span>
      </div>
    </div>
  </div>
  <div class="contentWrapper small">
    <div class="cols">
      <div class="col">
        <?php if (block_value("list-item-1")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-1") ?></div></div><?php } ?>
        <?php if (block_value("list-item-2")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-2") ?></div></div><?php } ?>
        <?php if (block_value("list-item-3")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-3") ?></div></div><?php } ?>
        <?php if (block_value("list-item-4")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-4") ?></div></div><?php } ?>
      </div>
      <div class="col">
        <?php if (block_value("list-item-5")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-5") ?></div></div><?php } ?>
        <?php if (block_value("list-item-6")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-6") ?></div></div><?php } ?>
        <?php if (block_value("list-item-7")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-7") ?></div></div><?php } ?>
        <?php if (block_value("list-item-8")){ ?><div class="checkItem"><div class="checkBox"></div><div class='innerText'><?php block_field("list-item-8") ?></div></div><?php } ?>
      </div>
    </div>
  </div>
</section>
