<section class="faqOverviewBlock">
  <div class="contentWrapper smaller">
    <!-- <h2 class="bigTitle"><?php block_field("title") ?> <?php block_value('category_ids') ?></h2> -->
    <div class="items">
      <?php
       $category = block_value("category-ids");
       $oldCategoryName = "";
        $args = array(
            'post_type'=> 'Faq',
            'category' => $category,
            'order'       => 'ASC',
            'orderby'     => 'category',
            'posts_per_page' => '100',
        );
        $myposts = get_posts( $args );
        foreach ( $myposts as $post ) : setup_postdata( $post );

        foreach((get_the_category()) as $category){
          $categoryName = $category->name;
          }

        if ($categoryName != $oldCategoryName) { ?>
          <h2 class="bigTitle"><?php echo($categoryName); ?></h2>
        <?php } ?>
        <div class="item">
          <div class="header"><span class="headerContent"><h3 class='normalTitle'><?php the_title() ?></h3></span><span class="headerIcon"></span></div>
          <div class="content">
            <div class="innerContent">
              <p><?php the_content() ?></p>

            </div>
          </div>
        </div>

         <?php
         foreach((get_the_category()) as $category){
           $oldCategoryName = $category->name;
          }
          endforeach;
          wp_reset_postdata();
          $oldCategoryName = "";
        ?>
        <?php
        $faqItems = [];
        $myposts = get_posts( $args );
        foreach ( $myposts as $post ) : setup_postdata( $post );
          $question = get_the_title();
          $answer = apply_filters('the_content', get_the_content());
          $faqItems[] = [
            "@type" => "Question",
            "name" => strip_tags($question),
            "acceptedAnswer" => [
              "@type" => "Answer",
              "text" => strip_tags($answer),
            ]
          ];
        endforeach;
        wp_reset_postdata();

        ?>
        <script type="application/ld+json">
        {
          "@context": "https://schema.org",
          "@type": "FAQPage",
          "mainEntity": <?php echo json_encode($faqItems, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT); ?>
        }
        </script>
  </div>
  </div>
</section>
