<section class="programmaOverviewBlock<?php if (block_value("no-margin-top")) { ?> noMarginTop<?php } ?><?php if (block_value("no-margin-bottom")) { ?> noMarginBottom<?php } ?>">
  <div class="contentWrapper small">
    <h2 class="bigTitle white upper"><?php block_field("title") ?></h2>
    <div class="items">
      <?php
      $loop = new WP_Query( array(
        'post_type' => 'artiest',
        'post_status' => 'publish',
        'orderby' => 'publish_date',
        'order' => 'ASC',
        'posts_per_page' => -1
      ) );
       while ( $loop->have_posts() ) : $loop->the_post();
        echo("<a href='".get_page_link()."' class='item'><span class='imgWrapper'>");
        echo(the_post_thumbnail());
        echo("<i class='icon-arrow-right'></i></span><h3 class='bigTitle'>");
        echo(the_title());
        echo("</h3></a>");
        endwhile; ?>
    </div>
  </div>
</section>
