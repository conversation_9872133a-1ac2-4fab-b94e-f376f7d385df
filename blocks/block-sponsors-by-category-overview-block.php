<section class="sponsorOverviewBlock yellow noMarginBottom noMarginTop noBorder" data-nosnippet>
  <div class="contentWrapper smaller">
    <div class="items">
      <?php
       $category = block_value("categories");
       $oldCategoryName = "";
        $args = array(
            'post_type'=> 'Sponsor',
            'category' => $category,
            'order'       => 'ASC',
            'orderby'     => 'category',
            'posts_per_page' => '100',
        );
        $myposts = get_posts( $args );
        foreach ( $myposts as $post ) : setup_postdata( $post );

        foreach((get_the_category()) as $category){
          $categoryName = $category->name;
          }

        if ($categoryName != $oldCategoryName) { ?>
          <h2 class="subTitle"><?php echo($categoryName); ?></h2>
        <?php } ?>
        <div class="item <?php echo($categoryName); ?>">
          <div class="innerWrapper">
            <img class="lazy" alt="<?php the_title() ?>" data-src="<?php the_post_thumbnail_url() ?>" aria-hidden="true">
          </div>
        </div>
         <?php
         foreach((get_the_category()) as $category){
           $oldCategoryName = $category->name;
          }
          endforeach;
          wp_reset_postdata();
          $oldCategoryName = "";
        ?>
  </div>
  </div>
</section>
