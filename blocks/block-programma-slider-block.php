<section class="programmaSliderBlock <?php block_field("background-color") ?><?php if (block_value("no-margin-bottom")) { ?> noMarginBottom noborderBottom<?php } ?>">
  <div class="contentWrapper small">
    <div class="cols">
      <div class="col">
        <h2 class="bigTitle white upper"><?php block_field("title") ?></h2>
      </div>
      <div class="col">
        <a href="<?php block_field("link-url") ?>" class="button white <?php block_field("background-color") ?>"><span class="innerText"><span><?php block_field("link-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("link-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("link-label") ?><i class="icon-arrow-right"></i></span></span></a>
      </div>
    </div>
    <div class="items">
      <?php
      $loop = new WP_Query( array(
        'post_type' => 'artiest',
        'posts_per_page' => '5',
        'orderby' => 'publish_date',
        'order' => 'ASC',
      ) );
       while ( $loop->have_posts() ) : $loop->the_post();
        echo("<a href='".get_page_link()."' class='item'><span class='imgWrapper'>");
        echo(the_post_thumbnail());
        echo("<i class='icon-arrow-right'></i></span><h3 class='bigTitle'>");
        echo(the_title());
        echo("</h3></a>");
        endwhile; ?>
        <div class="item mobile">
          <h3 class="bigTitle white upper">Bekijk hier het hele programma</h3>
          <a href="<?php block_field("link-url") ?>" class="button white <?php block_field("background-color") ?>"><span class="innerText"><span><?php block_field("link-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("link-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("link-label") ?><i class="icon-arrow-right"></i></span></span></a>
        </div>
    </div>
  </div>
</section>
