.contactBlock {
  // .cols {
  //   margin-left:-@vw50;
  //   white-space: nowrap;
  //   width: calc(100% ~"+" @vw100);
  //   .col {
  //     display: inline-block;
  //     vertical-align: middle;
  //     margin: 0 @vw50;
  //     white-space: normal;
  //     width: calc(50% ~"-" @vw100);
  //   }
  // }
  .imageWrapper {
    overflow: hidden;
    width: 100%;
    border-radius: @vw32;
    height: auto;
    .innerImage {
      height: 0;
      padding-bottom:123.35640138408304%;
      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
  .bigTitle {
    margin: @vw20 0;
    max-width: (@vw99 * 3) + (@vw22 * 4);
  }
  .button {
    margin-top: @vw30;
  }
  form {
    padding-top: @vw40;
    text-align: left;
    input, textarea, select {
      background: rgba(0,0,0,.05) !important;
      border: none;
      border-radius: 0;
      font-weight: 200;
      height: auto !important;
      padding: @vw16 @vw20;
      color: @hardBlack;
      font-size: @vw21;
      font-family: "filson-pro", arial, sans-serif !important;
    }
    input[type=submit]:focus {
      background: rgba(0,0,0,.05);
    }
    textarea {
      resize: none;
    }
    label {
      font-size: @vw16;
      color: @almostBlack;
    }
    button {
      cursor: pointer;
      background: @primaryColor;
      border: none;
      border-radius: 0;
      font-weight: 200;
      padding: @vw16 @vw20;
      color: @hardWhite;
      font-size: @vw21;
      font-family: "filson-pro", arial, sans-serif !important;
      .transition(.3s);
      &.disabled {
        opacity: .2;
        pointer-events: none;
      }
      &:hover {
        opacity: .7;
      }
    }
  }
  .ff-message-success {
    background: rgba(0,0,0,.1);
    border: none;
    border-radius: 0;
    font-weight: 200;
    padding: @vw16 @vw20;
    color: @hardBlack;
    font-size: @vw21;
    line-height: @vw31;
    font-family: "filson-pro", arial, sans-serif;
  }
}

@media all and (max-width: 1160px) {
  .contactBlock {
    .cols {
      margin-left:-@vw50-1160;
      width: calc(100% ~"+" @vw100-1160);
      .col {
        margin: 0 @vw50-1160;
        width: calc(50% ~"-" @vw100-1160);
      }
    }
    .imageWrapper {
      border-radius: @vw32-1160;
    }
    .bigTitle {
      margin: @vw20-1160 0;
      max-width: (@vw99-1160 * 3) + (@vw22-1160 * 4);
    }
    .button {
      margin-top: @vw30-1160;
    }
    form {
      padding-top: @vw40-1160;
      input, textarea, select {
        padding: @vw16-1160 @vw20-1160;
        font-size: @vw21-1160;
      }
      label {
        font-size: @vw16-1160;
      }
      button {
        padding: @vw16-1160 @vw20-1160;
        font-size: @vw21-1160;
      }
    }
    .ff-message-success {
      padding: @vw16-1160 @vw20-1160;
      font-size: @vw21-1160;
      line-height: @vw31-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .contactBlock {
    .cols {
      margin-left:-@vw50-580;
      white-space: normal;
      width: calc(100% ~"+" @vw100-580);
      .col {
        margin: 0 @vw50-580;
        width: calc(100% ~"-" @vw100-580);
        &:first-child {
          margin-bottom: @vw50-580;
        }
      }
    }
    .imageWrapper {
      border-radius: @vw32-580;
    }
    .bigTitle {
      margin: @vw20-580 0;
      max-width: (@vw99-580 * 3) + (@vw22-580 * 4);
    }
    .button {
      margin-top: @vw30-580;
    }
    form {
      padding-top: @vw40-580;
      input, textarea, select {
        padding: @vw16-580 @vw20-580;
        font-size: @vw21-580;
      }
      label {
        font-size: @vw21-580;
      }
      button {
        padding: @vw16-580 @vw20-580;
        font-size: @vw21-580;
      }
    }
    .ff-message-success {
      padding: @vw16-580 @vw20-580;
      font-size: @vw21-580;
      line-height: @vw31-580;
    }
  }
}
