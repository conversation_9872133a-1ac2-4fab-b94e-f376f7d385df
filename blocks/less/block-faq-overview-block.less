.faqOverviewBlock {
  .bigTitle {
    margin-top: @vw50;
    margin-bottom: @vw40;
    &:first-child {
      margin-top: 0;
    }
  }
  .items {
    .item {
      border-bottom: 1px dashed @green;
      margin-top: @vw20;
      &:first-child {
        margin-top: 0;
      }
      &.open {
        .header {
          .headerIcon {
            &:before {
              transform: translate(-50%,-50%) rotate(180deg);
            }
            &:after {
              transform: translate(-50%,-50%) rotate(135deg);
              opacity: 0;
            }
          }
        }
      }
      .header {
        cursor: pointer;
        padding-bottom: @vw20;
        .headerContent {
          cursor: pointer;
          display: inline-block;
          vertical-align: top;
          width: calc(100% ~"-" @vw40);
          .normalTitle {
            cursor: pointer;
          }
        }
        .headerIcon {
          cursor: pointer;
          display: inline-block;
          vertical-align: top;
          width: @vw40;
          height: @vw40;
          position: relative;
          &:before, &:after {
            content: '';
            cursor: pointer;
            position: absolute;
            top: 50%;
            left: 50%;
            width: @vw40;
            height: @vw5;
            background: @green;
            transform: translate(-50%,-50%);
            .transition(.3s);
          }
          &:after {
            transform: translate(-50%,-50%) rotate(90deg);
          }
        }
      }
      .content {
        overflow: hidden;
        position: relative;
        height: 0px;
        .transition(.3s);
        .innerContent {
          padding-bottom: @vw20;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .faqOverviewBlock {
    .bigTitle {
      margin-top: @vw50-1160;
      margin-bottom: @vw40-1160;
    }
    .items {
      .item {
        margin-top: @vw20-1160;
        .header {
          padding-bottom: @vw20-1160;
          .headerContent {
            width: calc(100% ~"-" @vw40-1160);
          }
          .headerIcon {
            width: @vw40-1160;
            height: @vw40-1160;
            &:before, &:after {
              width: @vw40-1160;
              height: @vw5-1160;
            }
          }
        }
        .content {
          .innerContent {
            padding-bottom: @vw20-1160;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .faqOverviewBlock {
    .bigTitle {
      margin-top: @vw50-580;
      margin-bottom: @vw40-580;
    }
    .items {
      .item {
        margin-top: @vw20-580;
        .header {
          padding-bottom: @vw20-580;
          .headerContent {
            width: calc(100% ~"-" @vw40-580);
          }
          .headerIcon {
            width: @vw40-580;
            height: @vw40-580;
            &:before, &:after {
              width: @vw40-580;
              height: @vw5-580;
            }
          }
        }
        .content {
          .innerContent {
            padding-bottom: @vw20-580;
          }
          img {
            margin-top: @vw20-580;
          }
        }
      }
    }
  }
}
