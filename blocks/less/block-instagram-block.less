// out: false
.instagramBlock {
  .bigTitle {
    margin-bottom: @vw40;
  }
  #sb_instagram #sbi_images {
    display: block;
    margin-left: -@vw20;
    width: calc(100% ~"+" @vw40);
  }
  #sb_instagram.sbi_col_4 #sbi_images .sbi_item {
    display: inline-block;
    height: auto !important;
    margin: 0 @vw20 !important;
    width: calc(25%~"-" @vw40);
    &:nth-child(even) {
      margin-top: @vw40 !important;
    }
    a {
      height: 0 !important;
      position: relative;
      padding-bottom: 124.99999999999999% !important;
    }
  }
  #sb_instagram .sbi_photo_wrap {
    height: 0;
    padding-bottom: 124.99999999999999%;
  }
  #sb_instagram #sbi_images .sbi_item.sbi_transition {
    opacity: 1;
  }
  #sb_instagram .sbi_photo img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

@media all and (max-width: 1160px) {
  .instagramBlock {
    .bigTitle {
      margin-bottom: @vw40-1160;
    }
    #sb_instagram #sbi_images {
      margin-left: -@vw10-1160;
      width: calc(100% ~"+" @vw20-1160);
    }
    #sb_instagram.sbi_col_4 #sbi_images .sbi_item {
      margin: 0 @vw10-1160 !important;
      width: calc(25%~"-" @vw20-1160);
      &:nth-child(even) {
        margin-top: @vw40-1160 !important;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .instagramBlock {
    .bigTitle {
      margin-bottom: @vw40-580;
    }
    #sb_instagram #sbi_images {
      margin-left: -@vw10-580;
      width: calc(100% ~"+" @vw20-580);
    }
    #sb_instagram.sbi_col_4 #sbi_images .sbi_item {
      margin: 0 @vw10-580 !important;
      margin-top: @vw20-580 !important;
      width: calc(50%~"-" @vw20-580);
      &:nth-child(even) {
        margin-top: @vw20-580 !important;
      }
    }
  }
}
