.latestNewsBlock {
  .cols {
    .col {
      display: inline-block;
      vertical-align: top;
      &:first-child {
        width: @vw744;
      }
      &:nth-child(2) {
        padding-left: @vw63;
        width: calc(100% ~"-" @vw744);
      }
      &.mobile {
        display: none;
      }
    }
  }
  .news {
    .newsItem {
      color: @lightGrey;
      padding: @vw30;
      cursor: pointer;
      position: relative;
      display: block;
      .transition(.3s);
      .innerCol {
        display: inline-block;
        vertical-align: top;
        &:first-child {
          border-right: 2px solid @backgroundGrey;
          width: 33.3333%;
        }
        &:last-child {
          padding-left: @vw20;
          padding-right: @vw40;
          width: 66.6666%;
        }
      }
      i {
        color: @lightGrey;
        position: absolute;
        right: 0;
        bottom: 0;
      }
      .bigTitle, p, i{
        .transition(.3s);
      }
      .text {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        height: @vw31 * 2;
        width: 100%;
        margin: @vw5 0 0 0;
      }
      &:hover {
        color: @blue;
        .bigTitle, p, i {
          color: @blue;
        }
        &:after {
          width: 100%;
        }
        &:before {
          width: 0;
        }
      }
      &:after, &:before {
        content: '';
        background: @lightGrey;
        top: auto;
        bottom: 0;
        left: auto;
        right: 0;
        height: @vw2;
        position: absolute;
        width: 100%;
        transition: width 0.6s cubic-bezier(0.83, 0, 0.17, 1);
      }
      &:after {
        left: 0;
        right: auto;
        width: 0%;
        background: @blue;
        transition: width 0.6s 0.15s cubic-bezier(0.83, 0, 0.17, 1);
      }
      * {
        cursor: pointer;
      }
    }
  }
  .subTitle {
    margin-bottom: @vw20;
  }
  .text {
    margin: @vw20 0 @vw40 0;
  }
}

@media all and (max-width: 1160px) {
  .latestNewsBlock {
    .cols {
      .col {
        &:first-child {
          width: 60%;
        }
        &:nth-child(2) {
          padding-left: @vw63-1160;
          width: 40%;
        }
      }
    }
    .news {
      .newsItem {
        padding: @vw30-1160;
        .innerCol {
          &:last-child {
            padding-left: @vw20-1160;
            padding-right: @vw40-1160;
          }
        }
        .text {
          height: @vw31-1160 * 2;
          margin: @vw5-1160 0 0 0;
        }
        &:after, &:before {
          height: @vw2-1160;
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-1160;
    }
    .text {
      margin: @vw20-1160 0 @vw40-1160 0;
    }
  }
}

@media all and (max-width: 580px) {
  .latestNewsBlock {
    .cols {
      .col {
        &:first-child {
          display: none;
        }
        &:nth-child(2) {
          margin-top: @vw50-580;
          padding-left: 0;
          width: 100%;
        }
        &.mobile {
          display: block;
        }
      }
    }
    .news {
      .newsItem {
        padding: @vw30-580 @vw10-580;
        .innerCol {
          &:last-child {
            padding-left: @vw10-580;
            padding-right: @vw30-580;
          }
        }
        .bigTitle {
          margin-bottom: @vw10-580;
        }
        .text {
          height: @vw31-580 * 2;
          margin: @vw5-580 0 0 0;
        }
        &:after, &:before {
          height: @vw2-580;
        }
      }
    }
    .subTitle {
      margin-bottom: @vw20-580;
    }
    .text {
      margin: @vw20-580 0 @vw40-580 0;
    }
  }
}
