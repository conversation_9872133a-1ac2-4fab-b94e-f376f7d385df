.textTwoColumnBlock {
  .cols {
    margin-left:-@vw50;
    white-space: nowrap;
    width: calc(100% ~"+" @vw100);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw50;
      white-space: normal;
      width: calc(50% ~"-" @vw100);
    }
  }
  .bigTitle {
    margin: @vw20 0;
    max-width: (@vw99 * 3) + (@vw22 * 4);
  }
  .button {
    margin-top: @vw30;
  }
}

@media all and (max-width: 1160px) {
  .textTwoColumnBlock {
    .cols {
      margin-left:-@vw50-1160;
      width: calc(100% ~"+" @vw100-1160);
      .col {
        margin: 0 @vw50-1160;
        width: calc(50% ~"-" @vw100-1160);
      }
    }
    .bigTitle {
      margin: @vw20-1160 0;
      max-width: (@vw99-1160 * 3) + (@vw22-1160 * 4);
    }
    .button {
      margin-top: @vw30-1160;
    }
  }
}

@media all and (max-width: 580px) {
  .textTwoColumnBlock {
    .cols {
      margin-left:-@vw50-580;
      white-space: normal;
      width: calc(100% ~"+" @vw100-580);
      .col {
        margin: 0 @vw50-580;
        width: calc(100% ~"-" @vw100-580);
        &:first-child {
          margin-bottom: @vw30-580;
        }
      }
    }
    .bigTitle {
      margin: @vw20-580 0;
      max-width: (@vw99-580 * 3) + (@vw22-580 * 4);
    }
    .button {
      margin-top: @vw30-580;
    }
  }
}
