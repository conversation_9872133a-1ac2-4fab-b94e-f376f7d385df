.newsHeaderBlock {
  margin-top: 0;
  padding-top: 0 !important;
  .background {
    &:after {
      content: '';
      height: @vw100 * 4;
      position: absolute;
      opacity: 1;
      left: 0;
      top: auto;
      bottom: 0;
      background: linear-gradient(rgba(255, 255, 255, 0), @hardWhite);
      width: 100%;
    }
  }
  .background {
    height: (@vw100 * 6) + @vw50 + @vw50;
    overflow: hidden;
    position: relative;
    width: 100%;
    .image, .video {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      left: 0;
      object-fit: cover;
    }
  }
  .textWrapper {
    position: absolute;
    left: 0;
    height: auto;
    width: 100%;
    bottom: @vw80;
    z-index: 2;
    .roundButton {
      position: absolute;
      right: (@vw99) + (@vw22 * 2);
      bottom: 0;
    }
    .button {
      margin-left: @vw20;
      &:first-child {
        margin-left: 0;
      }
    }
  }
  .contentWrapper {
    .bigTitle {
      color: @hardBlack;
      margin-bottom: @vw20;
      max-width: (@vw99 * 5) + (@vw22 * 4);
      text-transform: uppercase;
    }
  }
  .cols {
    margin-left:-@vw50;
    white-space: nowrap;
    width: calc(100% ~"+" @vw100);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw50;
      white-space: normal;
      width: calc(50% ~"-" @vw100);
    }
    p {
      color: @hardBlack;
    }
  }

}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@media all and (max-width: 1160px) {
  .newsHeaderBlock {
    .background {
      &:after {
        height: @vw100-1160 * 4;
      }
    }
    .background {
      height: (@vw100-1160 * 6) + @vw50-1160 + @vw50-1160;
    }
    .textWrapper {
      bottom: @vw80-1160;
      .roundButton {
        right: (@vw99-1160) + (@vw22-1160 * 2);
      }
      .button {
        margin-left: @vw20-1160;
      }
    }
    .contentWrapper {
      .bigTitle {
        margin-bottom: @vw20-1160;
        max-width: (@vw99-1160 * 5) + (@vw22-1160 * 4);
      }
    }
    .cols {
      margin-left:-@vw50-1160;
      width: calc(100% ~"+" @vw100-1160);
      .col {
        margin: 0 @vw50-1160;
        width: calc(50% ~"-" @vw100-1160);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .newsHeaderBlock {
    .background {
      &:after {
        height: @vw100-580 * 4;
      }
    }
    .background {
      height: (@vw100-580 * 6) + @vw50-580 + @vw50-580;
    }
    .textWrapper {
      bottom: @vw80-580;
      .roundButton {
        right: @vw22-580;
      }
      .button {
        margin-left: @vw20-580;
      }
    }
    .contentWrapper {
      .bigTitle {
        padding-right: @vw100-580 + @vw20-580;
        margin-bottom: @vw20-580;
        max-width: (@vw99-580 * 5) + (@vw22-580 * 4);
      }
    }
    .cols {
      margin-left:0;
      white-space: normal;
      width: 100%;
      .col {
        margin: 0;
        margin-bottom: @vw30-580;
        width: 100%;
        &:last-child {
          margin-bottom :0;
        }
      }
    }
  }
}
