.relatedArtistsBlock {
  .bigTitle {
    margin-bottom: @vw40;
  }
  .items {
    margin-left: -@vw10;
    white-space: nowrap;
    width: calc(100% ~"+" @vw20);
    .item {
      display: inline-block;
      margin: 0 @vw10;
      white-space: normal;
      width: calc(50% ~"-" @vw20);
      vertical-align: middle;
    }
  }
  .item {
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    position: relative;
    vertical-align: top;
    width: 100%;
    opacity: 1;
    &:hover {
      .imageWrapper {
        img {
          transform: scale(1.05);
        }
        i {
          right: @vw20 + @vw5;
        }
      }
    }
    .imageWrapper {
      cursor: pointer;
      display: block;
      border-radius: @vw40;
      overflow: hidden;
      width:100%;
      position: relative;
      height: auto;
      .innerImage {
        display: block;
        padding-bottom: 100%;
        width: 100%;
        height: 0;
        position: relative;
      }
      .normalTitle {
        cursor: pointer;
        position: absolute;
        bottom: @vw36;
        left: @vw30;
        line-height: @vw36 + @vw2;
        color: @hardWhite;
        width: calc(100% ~"-" @vw100);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      img {
        cursor: pointer;
        position: absolute;
        top: 0;
        height:100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform 0.6s cubic-bezier(0.22, 1, 0.36, 1);
      }
      i {
        cursor: pointer;
        position: absolute;
        top: auto;
        color: @hardWhite;
        font-size: @vw50;
        bottom: @vw30;
        right: @vw20;
        left: auto;
        transition: right 0.6s 0.15s cubic-bezier(0.22, 1, 0.36, 1);
      }
    }
    .bigTitle {
      cursor: pointer;
      margin-top: @vw10;
      padding: 0 @vw30;
      text-decoration: none;
      color: @hardWhite;
    }
  }
}

@media all and (max-width: 1160px) {
  .relatedArtistsBlock {
    .bigTitle {
      margin-bottom: @vw40-1160;
    }
    .items {
      margin-left: -@vw10-1160;
      width: calc(100% ~"+" @vw20-1160);
      .item {
        margin: 0 @vw10-1160;
        width: calc(50% ~"-" @vw20-1160);
      }
    }
    .item {
      &:hover {
        .imageWrapper {
          i {
            right: @vw20-1160 + @vw5-1160;
          }
        }
      }
      .imageWrapper {
        border-radius: @vw40-1160;
        .normalTitle {
          bottom: @vw36-1160;
          left: @vw30-1160;
          line-height: @vw36-1160 + @vw2-1160;
          width: calc(100% ~"-" @vw100-1160);
        }
        i {
          font-size: @vw50-1160;
          bottom: @vw30-1160;
          right: @vw20-1160;
        }
      }
      .bigTitle {
        margin-top: @vw10-1160;
        padding: 0 @vw30-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .relatedArtistsBlock {
    .bigTitle {
      margin-bottom: @vw40-580;
    }
    .items {
      margin-left: -@vw10-580;
      white-space: normal;
      width: calc(100% ~"+" @vw20-580);
      .item {
        margin: 0 @vw10-580;
        display: none;
        width: calc(100% ~"-" @vw20-580);
        &:first-child {
          display: block;
        }
      }
    }
    .item {
      &:hover {
        .imageWrapper {
          i {
            right: @vw20-580 + @vw5-580;
          }
        }
      }
      .imageWrapper {
        border-radius: @vw40-580;
        .normalTitle {
          bottom: @vw36-580;
          left: @vw30-580;
          line-height: @vw36-580 + @vw2-580;
          width: calc(100% ~"-" @vw100-580);
        }
        i {
          font-size: @vw50-580;
          bottom: @vw30-580;
          right: @vw20-580;
        }
      }
      .bigTitle {
        margin-top: @vw10-580;
        padding: 0 @vw30-580;
      }
    }
  }
}
