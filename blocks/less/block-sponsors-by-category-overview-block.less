.sponsorOverviewBlock {
  .bigTitle {
    margin-top: @vw50;
    margin-bottom: @vw40;
    &:first-child {
      margin-top: 0;
    }
  }
  .subTitle {
    display: block;
    background: rgba(255,255,255,.2);
    margin-bottom: @vw40;
    color: @hardWhite;
  }
  .items {
    margin-bottom: -@vw20;
    margin-left: -@vw10;
    width: calc(100% ~"+" @vw20);
    .item {
      background: rgba(255,255,255,1);
      border-radius: @vw40;
      height: auto;
      padding: @vw40;
      position: relative;
      display: inline-block;
      margin: 0 @vw10;
      overflow: hidden;
      margin-bottom: @vw20;
      width: calc(33.3333% ~"-" @vw20);
      vertical-align: middle;
      text-align: center;
      &.Zilver {
        .innerWrapper {
          padding-bottom: 50%;
          img {
            max-width: 80%;
            max-height: 100%;
          }
        }
      }
      .innerWrapper {
        height: 0;
        padding-bottom: 100%;
        width: 100%;
      }
      &:first-child {
        margin-top: 0;
      }
      img {
        display: block;
        margin: auto;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .sponsorOverviewBlock {
    .bigTitle {
      margin-top: @vw50-1160;
      margin-bottom: @vw40-1160;
    }
    .subTitle {
      margin-bottom: @vw40-1160;
    }
    .items {
      margin-bottom: -@vw20-1160;
      margin-left: -@vw10-1160;
      width: calc(100% ~"+" @vw20-1160);
      .item {
        border-radius: @vw40-1160;
        padding: @vw40-1160;
        margin: 0 @vw10-1160;
        margin-bottom: @vw20-1160;
        width: calc(33.3333% ~"-" @vw20-1160);
        &.Zilver {
          .innerWrapper {
            padding-bottom: 50%;
            img {
              max-width: 80%;
              max-height: 100%;
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .sponsorOverviewBlock {
    .bigTitle {
      margin-top: @vw50-580;
      margin-bottom: @vw40-580;
    }
    .subTitle {
      margin-bottom: @vw40-580;
    }
    .items {
      margin-bottom: -@vw20-580;
      margin-left: -@vw10-580;
      width: calc(100% ~"+" @vw20-580);
      .item {
        border-radius: @vw40-580;
        padding: @vw20-580;
        margin: 0 @vw10-580;
        margin-bottom: @vw20-580;
        width: calc(50% ~"-" @vw20-580);
        &.Zilver {
          .innerWrapper {
            padding-bottom: 50%;
            img {
              max-width: 80%;
              max-height: 100%;
            }
          }
        }
      }
    }
  }
}
