.imageBiggerBlock {
  .imageWrapper {
    overflow: hidden;
    width: 100%;
    border-radius: @vw32;
    height: auto;
    .innerImage {
      height: 0;
      padding-bottom:46.88552188552188%;
      img {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .imageBiggerBlock {
    .imageWrapper {
      border-radius: @vw32-1160;
      .innerImage {
        padding-bottom:46.88552188552188%;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .imageBiggerBlock {
    .imageWrapper {
      border-radius: @vw32-580;
      .innerImage {
        padding-bottom:100%;
      }
    }
  }
}
