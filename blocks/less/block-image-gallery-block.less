// out: false
html body .modula .modula-items .modula-item .modula-item-content {
  opacity: 1 !important;
}

.modula-gallery:not( .modula-gallery-initialized )>.modula-items {
  visibility: visible !important;
}

.imageGalleryBlock {
  .contentWrapper {
    .modula-items {
      height: auto !important;
      display: block;
      margin-left: calc(-@vw22 ~"/" 2);
      width: calc(100% ~"+" @vw22) !important;
      .modula-item {
        border-radius: @vw32;
        overflow: hidden;
        display: inline-block;
        vertical-align: top;
        position: relative !important;
        margin: 0 calc(@vw22 / 2);
        margin-bottom: @vw40;
        height: auto !important;
        left: 0 !important;
        top: 0 !important;
        transform: translate3d(0,0,0) !important;
        width: calc(66.6666% ~"-" @vw22) !important;
        &.small {
          width: calc(33.3333% ~"-" @vw22) !important;
        }
        .modula-item-content {
          height: @vw100 * 7 !important;
          // padding-bottom: 100%;
          width: 100% !important;
          img {
            position: absolute;
            inset: auto !important;
            top: 0;
            left: 0;
            max-width: 100% !important;
            width: 100% !important;
            height: 100% !important;
            object-fit: cover;
            object-position: center;
          }
        }
      }
    }
  }
  .post-edit-link {
    display: none !important;
  }
}

@media all and (max-width: 1160px) {
  .imageGalleryBlock {
    .contentWrapper {
      .modula-items {
        margin-left: calc(-@vw22-1160 / 2);
        width: calc(100% ~"+" @vw22-1160) !important;
        .modula-item {
          border-radius: @vw32-1160;
          margin: 0 calc(@vw22-1160 / 2);
          margin-bottom: @vw40-1160;
          width: calc(66.6666% ~"-" @vw22-1160) !important;
          &.small {
            width: calc(33.3333% ~"-" @vw22-1160) !important;
          }
          .modula-item-content {
            height: @vw100-1160 * 5 !important;
          }
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .imageGalleryBlock {
    .contentWrapper {
      .modula-items {
        margin-left: calc(-@vw22-580 / 2);
        width: calc(100% ~"+" @vw22-580) !important;
        .modula-item {
          border-radius: @vw32-580;
          margin: 0 calc(@vw22-580 / 2);
          margin-bottom: @vw40-580;
          width: calc(100% ~"-" @vw22-580) !important;
          &.small {
            width: calc(100% ~"-" @vw22-580) !important;
          }
          .modula-item-content {
            height: @vw100-580 * 6 !important;
          }
        }
      }
    }
  }
}
