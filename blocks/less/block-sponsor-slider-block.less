.sponsorSliderBlock {
  padding: @vw40 0;
  background: @yellow;
  height: auto;
  margin: 0;
  position: relative;
  &:after {
    content: '';
    position: absolute;
    top: 1px;
    transform:(translateY(-100%));
    height: 0;
    background: @hardWhite;
    width:100%;
    padding-bottom: 35.82175925925926%;
    z-index: -4;
  }
  .cols {
    margin-bottom: @vw100;
    .col {
      display: inline-block;
      width: 50%;
      vertical-align: top;
      &:last-child {
        text-align: right;
      }
    }
  }
  .text {
    color: @hardWhite;
    margin-top: @vw20;
  }
  .slider {
    left: 0;
    top: 0;
    white-space: nowrap;
    width: 100%;
    .innerSlider {
      position: relative;
    }
    .animItem {
      display: inline-block;
      vertical-align: middle;
      &.animate {
        animation: 80s animateToLeft infinite linear;
      }
    }
    .item {
      background: rgba(255,255,255,1);
      border-radius: @vw40;
      padding: @vw40;
      position: relative;
      margin-bottom: @vw20;
      white-space: normal;
      display: inline-block;
      margin: 0 @vw30;
      height: auto;
      width: @vw100 * 2;
      vertical-align: middle;
      overflow: hidden;
      .innerWrapper {
        height: 0;
        padding-bottom: 100%;
        width: 100%;
      }
      img {
        display: block;
        margin: auto;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-width: 90%;
      }
    }
  }
}

@keyframes animateToLeft {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@media all and (max-width: 1160px) {
  .sponsorSliderBlock {
    // padding: @vw40 0;
    .cols {
      margin-bottom: @vw100-1160;
    }
    .text {
      margin-top: @vw20-1160;
    }
    .slider {
      .item {
        border-radius: @vw40-1160;
        padding: @vw40-1160;
        margin: 0 @vw100-1160;
        margin-bottom: @vw20-1160;
        width: @vw100-1160 * 1.5;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .sponsorSliderBlock {
    .cols {
      margin-bottom: @vw100-580;
      .col {
        display: block;
        width: 100%;
        &:last-child {
          margin-top: @vw30-580;
          text-align: left;
        }
      }
    }
    .text {
      margin-top: @vw20-580;
    }
    .slider {
      .item {
        border-radius: @vw40-580;
        padding: @vw20-580;
        margin: 0 @vw30-580;
        width: @vw100-580 * 1.5;
      }
    }
  }
}
