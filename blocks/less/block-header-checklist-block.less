.headerChecklistBlock {
  margin-top: 0;
  padding-top: 0 !important;
  &.pink, &.yellow, &.green, &.blue {
    .background {
      &:after {
        content: '';
        height: @vw100 * 4;
        position: absolute;
        opacity: 1;
        left: 0;
        top: auto;
        bottom: 0;
        width: 100%;
      }
    }
  }
  &.pink {
    .background {
      &:after {
        background: linear-gradient(rgba(237, 39, 143, 0), @pink);
      }
    }
  }
  &.yellow {
    .background {
      &:after {
        background: linear-gradient(rgba(216, 161, 4, 0), @yellow);
      }
    }
  }
  &.blue {
    .background {
      &:after {
        background: linear-gradient(rgba(58, 42, 245, 0), @blue);
      }
    }
  }
  &.green {
    .background {
      &:after {
        background: linear-gradient(rgba(63, 167, 69, 0), @green);
      }
    }
  }
  &.borderRadiusBottom {
    &:first-child {
      border-radius: 0 0 @vw32 @vw32;
    }
  }
  .background {
    height: calc(100vh ~"-" @vw87 ~"-" @vw87);
    overflow: hidden;
    position: relative;
    width: 100%;
    &:after {
      content: '';
      height: 100%;
      position: absolute;
      opacity: .3;
      left: 0;
      top: 0;
      width: 100%;
      background: @darkerGrey;
    }
    .image, .video {
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      left: 0;
      object-fit: cover;
    }
  }
  .textWrapper {
    position: absolute;
    left: 0;
    height: auto;
    width: 100%;
    bottom: @vw80;
    z-index: 2;
    .roundButton {
      position: absolute;
      right: (@vw99) + (@vw22 * 2);
      bottom: 0;
    }
    .button {
      margin-left: @vw20;
      &:first-child {
        margin-left: 0;
      }
    }
  }
  .contentWrapper {
    .bigTitle {
      color: @almostWhite;
      margin-bottom: @vw20;
      max-width: @vw748;
      text-transform: uppercase;
    }
  }
  .checkItem {
    cursor: pointer;
    display: block;
    margin-bottom: @vw10;
    &:last-child {
      margin-bottom: 0;
    }
    &.active {
      .checkBox {
        &:after {
          opacity: 1;
        }
      }
    }
    .checkBox {
      cursor: pointer;
      display: inline-block;
      height: @vw22;
      width: @vw22;
      border-radius: @vw5;
      border: 1px solid @hardWhite;
      vertical-align: middle;
      margin-right: @vw22;
      position: relative;
      &:after {
        .transition(.3s);
        opacity: 0;
        content: '';
        position: absolute;
        border-radius: @vw2;
        height: @vw10;
        background: @hardWhite;
        width: @vw10;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
    .innerText {
      display: inline-block;
      vertical-align: top;
      width: calc(100% ~"-" @vw22 ~"-" @vw22);
    }
  }
  .cols {
    margin-left:-@vw50;
    white-space: nowrap;
    width: calc(100% ~"+" @vw100);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw50;
      white-space: normal;
      width: calc(50% ~"-" @vw100);
    }
    p {
      color: @hardWhite;
    }
  }

}

@keyframes rotate {
  to {
    transform: rotate(360deg);
  }
}

@media all and (max-width: 1160px) {
  .headerChecklistBlock {
    &.pink, &.yellow, &.green, &.blue {
      .background {
        &:after {
          height: @vw100-1160 * 4;
        }
      }
    }
    &.borderRadiusBottom {
      &:first-child {
        border-radius: 0 0 @vw32-1160 @vw32-1160;
      }
    }
    .background {
      height: calc(100vh ~"-" @vw87-1160 ~"-" @vw87-1160);
    }
    .textWrapper {
      bottom: @vw80-1160;
      .roundButton {
        right: (@vw99-1160) + (@vw22-1160 * 2);
      }
      .button {
        margin-left: @vw20-1160;
      }
    }
    .contentWrapper {
      .bigTitle {
        margin-bottom: @vw20-1160;
        max-width: @vw748-1160;
      }
    }
    .checkItem {
      margin-bottom: @vw10-1160;
      .checkBox {
        height: @vw22-1160;
        width: @vw22-1160;
        border-radius: @vw5-1160;
        margin-right: @vw22-1160;
        &:after {
          border-radius: @vw2-1160;
          height: @vw10-1160;
          width: @vw10-1160;
        }
      }
      .innerText {
        width: calc(100% ~"-" @vw22-1160 ~"-" @vw22-1160);
      }
    }
    .cols {
      margin-left:-@vw50-1160;
      width: calc(100% ~"+" @vw100-1160);
      .col {
        margin: 0 @vw50-1160;
        width: calc(50% ~"-" @vw100-1160);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .headerChecklistBlock {
    &.pink, &.yellow, &.green, &.blue {
      .background {
        &:after {
          height: @vw100-580 * 4;
        }
      }
    }
    &.borderRadiusBottom {
      &:first-child {
        border-radius: 0 0 @vw32-580 @vw32-580;
      }
    }
    .background {
      height: calc(100vh ~"-" @vw87-580 ~"-" @vw87-580);
    }
    .textWrapper {
      bottom: @vw80-580;
      .roundButton {
        right: @vw22-580;
      }
      .button {
        margin-left: @vw20-580;
      }
    }
    .contentWrapper {
      .bigTitle {
        margin-bottom: @vw20-580;
        max-width: 100%;
        padding-right: @vw100-580 + @vw20-580;
      }
    }
    .checkItem {
      margin-bottom: @vw20-580;
      .checkBox {
        height: @vw22-580;
        width: @vw22-580;
        border-radius: @vw5-580;
        margin-right: @vw22-580;
        &:after {
          border-radius: @vw2-580;
          height: @vw10-580;
          width: @vw10-580;
        }
      }
      .innerText {
        width: calc(100% ~"-" @vw22-580 ~"-" @vw22-580);
      }
    }
    .cols {
      margin-left:0;
      white-space: normal;
      width: 100%;
      .col {
        margin: 0;
        width: 100%;
        &:first-child {
          margin-bottom: @vw20-580;
        }
      }
    }
  }
}
