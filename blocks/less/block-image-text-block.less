.imageTextBlock {
  .cols {
    white-space: nowrap;
    .col {
      display: inline-block;
      vertical-align: middle;
      white-space: normal;
      width: 50%;
      &:last-child {
        padding-left: @vw134;
      }
      .imageWrapper {
        overflow: hidden;
        width: 100%;
        border-radius: @vw32;
        height: auto;
        .innerImage {
          height: 0;
          padding-bottom:123.35640138408304%;
          img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
          }
        }
      }
      .subTitle {
        margin-bottom: @vw20;
      }
      .text {
        margin: @vw20 0 @vw40 0;
      }
      .button {
        margin-right: @vw10;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}

@media all and (max-width: 1160px) {
  .imageTextBlock {
    .cols {
      .col {
        &:last-child {
          padding-left: @vw80-1160;
        }
        .imageWrapper {
          border-radius: @vw32-1160;
          .innerImage {
            padding-bottom:123.35640138408304%;
          }
        }
        .subTitle {
          margin-bottom: @vw20-1160;
        }
        .text {
          margin: @vw20-1160 0 @vw40-1160 0;
        }
        .button {
          margin-right: @vw10-1160;
        }
      }
    }
  }
}

@media all and (max-width: 580px) {
  .imageTextBlock {
    .cols {
      .col {
        display: block;
        width: 100%;
        &:last-child {
          margin-top: @vw40-580;
          padding-left: 0;
        }
        .imageWrapper {
          border-radius: @vw32-580;
          .innerImage {
            padding-bottom:100%;
          }
        }
        .subTitle {
          margin-bottom: @vw20-580;
        }
        .text {
          margin: @vw20-580 0 @vw40-580 0;
        }
        .button {
          margin-right: @vw10-580;
        }
      }
    }
  }
}
