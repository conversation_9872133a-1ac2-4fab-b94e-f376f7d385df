.pin-spacer {
  overflow: hidden !important;
}
.programmaOverviewBlock {
  // margin-bottom: 100vh;
  display: block;
  background: linear-gradient(@pink, @blue);
  padding: @vw80 + @vw40 0;
  color: @hardWhite;
  .bigTitle {
    margin-bottom: @vw40;
  }
  .items {
    left: 0;
    .transition(.15s);
    margin-left: -@vw30;
    width: calc(100% ~"+" @vw60);
  }
  .item {
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 0 @vw30;
    position: relative;
    vertical-align: top;
    width: calc(50% ~"-" @vw60);
    opacity: 1;
    &:first-child {
      display: block;
      width: calc(100% ~"-" @vw60);
      .imgWrapper {
        height: @vw100 * 7;
        img {
          object-position: center;
        }
      }
    }
    &:hover {
      .imgWrapper {
        img {
          transform: scale(1.05);
        }
        i {
          left: @vw30 + @vw5;
        }
      }
    }
    .imgWrapper {
      cursor: pointer;
      display: block;
      border-radius: @vw40;
      overflow: hidden;
      width:100%;
      height: @vw100 * 5.12;
      img {
        cursor: pointer;
        position: absolute;
        top: 0;
        height:100%;
        width: 100%;
        object-fit: cover;
        object-position: top;
        transition: transform 0.6s cubic-bezier(0.22, 1, 0.36, 1);
      }
      i {
        position: absolute;
        top: auto;
        color: @hardWhite;
        font-size: @vw63;
        bottom: @vw30;
        left: @vw30;
        transition: left 0.6s 0.15s cubic-bezier(0.22, 1, 0.36, 1);
      }
    }
    .bigTitle {
      cursor: pointer;
      margin-top: @vw10;
      padding: 0 @vw30;
      text-decoration: none;
      color: @hardWhite;
    }
  }
}

@media all and (max-width: 1160px) {
  .programmaOverviewBlock {
    padding: @vw80-1160 + @vw40-1160 0;
    .bigTitle {
      margin-bottom: @vw40-1160;
    }
    .items {
      margin-left: -@vw30-1160;
      width: calc(100% ~"+" @vw60-1160);
    }
    .item {
      margin: 0 @vw30-1160;
      width: calc(50% ~"-" @vw60-1160);
      &:first-child {
        width: calc(100% ~"-" @vw60-1160);
        .imgWrapper {
          height: @vw100-1160 * 7;
        }
      }
      &:hover {
        .imgWrapper {
          i {
            left: @vw30-1160 + @vw5-1160;
          }
        }
      }
      .imgWrapper {
        border-radius: @vw40-1160;
        height: @vw100-1160 * 5.12;
        i {
          font-size: @vw63-1160;
          bottom: @vw30-1160;
          left: @vw30-1160;
        }
      }
      .bigTitle {
        margin-top: @vw10-1160;
        padding: 0 @vw30-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .programmaOverviewBlock {
    padding: @vw80-580 + @vw40-580 0;
    .bigTitle {
      margin-bottom: @vw40-580;
    }
    .items {
      margin-left: -@vw30-580;
      width: calc(100% ~"+" @vw60-580);
    }
    .item {
      margin: 0 @vw30-580;
      width: calc(100% ~"-" @vw60-580);
      &:first-child {
        width: calc(100% ~"-" @vw60-580);
        .imgWrapper {
          height: @vw100-580 * 5.12;
        }
      }
      &:hover {
        .imgWrapper {
          i {
            left: @vw30-580 + @vw5-580;
          }
        }
      }
      .imgWrapper {
        border-radius: @vw40-580;
        height: @vw100-580 * 5.12;
        i {
          font-size: @vw63-580;
          bottom: @vw30-580;
          left: @vw30-580;
        }
      }
      .bigTitle {
        margin-top: @vw10-580;
        padding: 0 @vw30-580;
      }
    }
  }
}
