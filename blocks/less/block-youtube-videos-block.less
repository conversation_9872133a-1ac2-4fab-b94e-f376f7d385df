.youtubeVideosBlock {
  .bigTitle {
    margin-bottom: @vw40;
  }
  .cols {
    margin-left:-@vw50 / 2;
    width: calc(100% ~"+" @vw50);
    .col {
      display: inline-block;
      vertical-align: top;
      margin: 0 @vw50 / 2;
      width: calc(50% ~"-" @vw50);
      .wrapper {
        position: relative;
        height: 0;
        width: 100%;
        padding-bottom: 63.35403726708074%;
        iframe {
          position: absolute;
          width: 100%;
          height: 100%;
        }
      }
    }
    p {
      color: @hardWhite;
    }
  }
}

@media all and (max-width: 1160px) {
  .youtubeVideosBlock {
    .bigTitle {
      margin-bottom: @vw40-1160;
    }
    .cols {
      margin-left:-@vw50-1160 / 2;
      width: calc(100% ~"+" @vw50-1160);
      .col {
        margin: 0 @vw50-1160 / 2;
        width: calc(50% ~"-" @vw50-1160);
      }
    }
  }
}

@media all and (max-width: 580px) {
  .youtubeVideosBlock {
    .bigTitle {
      margin-bottom: @vw40-580;
    }
    .cols {
      margin-left:-@vw50-580 / 2;
      width: calc(100% ~"+" @vw50-580);
      .col {
        display: block;
        margin: 0 @vw50-580 / 2;
        margin-bottom: @vw20-580;
        width: calc(100% ~"-" @vw50-580);
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
