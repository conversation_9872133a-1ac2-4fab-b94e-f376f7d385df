.pin-spacer {
  overflow: hidden !important;
}
.programmaSliderBlock {
  // margin-bottom: 100vh;
  display: block;
  background: @pink;
  padding: @vw80 + @vw40 0;
  color: @hardWhite;
  .cols {
    margin-bottom: @vw40;
    .col {
      display: inline-block;
      width: 50%;
      vertical-align: middle;
      &:last-child {
        text-align: right;
      }
    }
  }
  .items {
    left: 0;
    .transition(.15s);
    white-space: nowrap;
    margin-left: -@vw30;
    width: calc(100% ~"+" @vw60);
  }
  .item {
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    margin: 0 @vw30;
    white-space: normal;
    position: relative;
    vertical-align: top;
    width: calc(50% ~"-" @vw60);
    opacity: 1;
    &.mobile {
      display: none;
      visibility: hidden;
    }
    &:hover {
      .imgWrapper {
        img {
          transform: scale(1.05);
        }
        i {
          left: @vw30 + @vw5;
        }
      }
    }
    .imgWrapper {
      cursor: pointer;
      display: block;
      border-radius: @vw40;
      overflow: hidden;
      width:100%;
      // height: @vw100 * 5.12;
      height: 55vh;
      img {
        cursor: pointer;
        position: absolute;
        top: 0;
        height:100%;
        width: 100%;
        object-fit: cover;
        object-position: center;
        transition: transform 0.6s cubic-bezier(0.22, 1, 0.36, 1);
      }
      i {
        position: absolute;
        top: auto;
        color: @hardWhite;
        font-size: @vw63;
        bottom: @vw30;
        left: @vw30;
        transition: left 0.6s 0.15s cubic-bezier(0.22, 1, 0.36, 1);
      }
    }
    .bigTitle {
      cursor: pointer;
      margin-top: @vw10;
      padding: 0 @vw30;
      text-decoration: none;
      color: @hardWhite;
    }
  }
}

@media all and (max-width: 1160px) {
  .programmaSliderBlock {
    padding: @vw80-1160 + @vw40-1160 0;
    .cols {
      margin-bottom: @vw40-1160;
    }
    .items {
      margin-left: -@vw30-1160;
      width: calc(100% ~"+" @vw60-1160);
    }
    .item {
      margin: 0 @vw30-1160;
      width: calc(50% ~"-" @vw60-1160);
      &:hover {
        .imgWrapper {
          i {
            left: @vw30-1160 + @vw5-1160;
          }
        }
      }
      .imgWrapper {
        border-radius: @vw40-1160;
        // height: @vw100-1160 * 5.12;
        i {
          font-size: @vw63-1160;
          bottom: @vw30-1160;
          left: @vw30-1160;
        }
      }
      .bigTitle {
        margin-top: @vw10-1160;
        padding: 0 @vw30-1160;
      }
    }
  }
}

@media all and (max-width: 580px) {
  .programmaSliderBlock {
    padding: @vw80-580 + @vw40-580 0;
    .cols {
      margin-bottom: @vw40-580;
      .col {
        display: block;
        width: 100%;
        &:nth-child(2) {
          display: none;
        }
      }
    }
    .items {
      white-space: normal;
      margin-left: -@vw30-580;
      width: calc(100% ~"+" @vw60-580);
    }
    .item {
      display: block;
      margin: 0 @vw30-580;
      margin-bottom: @vw60-580;
      width: calc(100% ~"-" @vw60-580);
      &:last-child {
        margin-bottom: 0;
      }
      &.mobile {
        visibility: visible;
        display: inline-block;
        .bigTitle {
          margin-top: 0;
          padding: 0;
          padding-top: @vw99-580;
        }
        .button {
          margin-top: @vw30-580;
        }
      }
      &:hover {
        .imgWrapper {
          i {
            left: @vw30-580 + @vw5-580;
          }
        }
      }
      .imgWrapper {
        border-radius: @vw40-580;
        height: @vw100-580 * 5;
        i {
          font-size: @vw63-580;
          bottom: @vw30-580;
          left: @vw30-580;
        }
      }
      .bigTitle {
        margin-top: @vw10-580;
        padding: 0 @vw30-580;
      }
    }
  }
}
