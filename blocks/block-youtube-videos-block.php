<section class="youtubeVideosBlock">
  <div class="contentWrapper small">
    <h2 class="bigTitle upper"><?php block_field("title") ?></h2>
    <div class="cols">
      <div class="col">
        <div class="wrapper">
          <?php
          if (block_value('youtube-video-1')) { $link1 = getEmbedUrl(block_value('youtube-video-1')); }
          if (block_value('youtube-video-2')) { $link2 = getEmbedUrl(block_value('youtube-video-2')); }
          if (block_value('youtube-video-3')) { $link3 = getEmbedUrl(block_value('youtube-video-3')); }
          if (block_value('youtube-video-4')) { $link4 = getEmbedUrl(block_value('youtube-video-4')); }
          if (block_value('youtube-video-1')) {
            $text1 = block_value('youtube-video-1');
          }
          if (block_value('youtube-video-2')) {
            $text2 = block_value('youtube-video-2');
          }
           if (block_value('youtube-video-1')) {
             echo '<iframe height="315"  width="100%" src="'.$link1.'" frameborder="0" allowfullscreen></iframe>';
           }
           ?>
         </div>
      </div>
      <div class="col">
        <div class="wrapper">
          <?php
          if (block_value('youtube-video-2')) {
            echo '<iframe height="315"  width="100%" src="'.$link2.'" frameborder="0" allowfullscreen></iframe>';
          }
          ?>
        </div>
      </div>
    </div>
  </div>
</section>


<?php function getEmbedUrl($url) {
    // function for generating an embed link
    $finalUrl = '';

    if (strpos($url, 'facebook.com/') !== false) {
        // Facebook Video
        $finalUrl.='https://www.facebook.com/plugins/video.php?href='.rawurlencode($url).'&show_text=1&width=200';

    } else if(strpos($url, 'vimeo.com/') !== false) {
        // Vimeo video
        $videoId = isset(explode("vimeo.com/",$url)[1]) ? explode("vimeo.com/",$url)[1] : null;
        if (strpos($videoId, '&') !== false){
            $videoId = explode("&",$videoId)[0];
        }
        $finalUrl.='https://player.vimeo.com/video/'.$videoId;

    } else if (strpos($url, 'youtube.com/') !== false) {
        // Youtube video
        $videoId = isset(explode("v=",$url)[1]) ? explode("v=",$url)[1] : null;
        if (strpos($videoId, '&') !== false){
            $videoId = explode("&",$videoId)[0];
        }
        $finalUrl.='https://www.youtube.com/embed/'.$videoId;

    } else if(strpos($url, 'youtu.be/') !== false) {
        // Youtube  video
        $videoId = isset(explode("youtu.be/",$url)[1]) ? explode("youtu.be/",$url)[1] : null;
        if (strpos($videoId, '&') !== false) {
            $videoId = explode("&",$videoId)[0];
        }
        $finalUrl.='https://www.youtube.com/embed/'.$videoId;

    } else if (strpos($url, 'dailymotion.com/') !== false) {
        // Dailymotion Video
        $videoId = isset(explode("dailymotion.com/",$url)[1]) ? explode("dailymotion.com/",$url)[1] : null;
        if (strpos($videoId, '&') !== false) {
            $videoId = explode("&",$videoId)[0];
        }
        $finalUrl.='https://www.dailymotion.com/embed/'.$videoId;

    } else{
        $finalUrl.=$url;
    }

    return $finalUrl;
}
 ?>
