<section class="sponsorSliderBlock yellow" data-nosnippet>
  <div class="contentWrapper small">
    <div class="cols">
      <div class="col">
        <h2 class="bigTitle white"><?php block_field("title") ?></h2>
        <p class="text white"><?php block_field("text") ?></p>
      </div>
      <div class="col">
        <a href="<?php block_field("button-url") ?>" class="button white yellow"><span class="innerText"><span><?php block_field("button-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("button-label") ?><i class="icon-arrow-right"></i></span><span><?php block_field("button-label") ?><i class="icon-arrow-right"></i></span></span></a>
      </div>
    </div>
  </div>
  <div class="slider">
    <div class="innerSlider">
      <div class="animItem">
        <?php
          $args = array(
              'post_type'=> 'Sponsor',
              'order'       => 'ASC',
              'orderby'     => 'category',
              'posts_per_page' => '100',
          );
          $myposts = get_posts( $args );
          foreach ( $myposts as $post ) : setup_postdata( $post );?>
          <div class="item">
            <div class="innerWrapper">
              <img alt="<?php the_title() ?>" src="<?php the_post_thumbnail_url() ?>" aria-hidden="true">
            </div>
          </div>
           <?php
            endforeach;
            wp_reset_postdata();
          ?>
      </div>
      <div class="animItem">
        <?php
          $myposts = get_posts( $args );
          foreach ( $myposts as $post ) : setup_postdata( $post );?>
          <div class="item">
            <div class="innerWrapper">
              <img alt="<?php the_title() ?>" src="<?php the_post_thumbnail_url() ?>" aria-hidden="true">
            </div>
          </div>
           <?php
            endforeach;
            wp_reset_postdata();
          ?>
      </div>
    </div>
  </div>
</section>
