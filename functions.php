<?php

  function jn_files(){
    // load scripts
    wp_enqueue_script('Jquery', get_theme_file_uri('/libs/jquery.min.js'), NULL, '1.0', true);
    wp_enqueue_script('<PERSON><PERSON>', get_theme_file_uri('/libs/lenis.min.js'), NULL, '1.0', true);
    wp_enqueue_script('Swup', get_theme_file_uri('/libs/swup.js'), NULL, '1.0', true);
    wp_enqueue_script('Swup head', get_theme_file_uri('/libs/swup_head.js'), NULL, '1.0', true);
    wp_enqueue_script('Swup Gtag', get_theme_file_uri('/libs/swup_gtag.js'), NULL, '1.0', true);
    wp_enqueue_script('GSAP', get_theme_file_uri('/libs/gsap.min.js'), NULL, '1.0', true);
    wp_enqueue_script('Custom Ease', get_theme_file_uri('/libs/CustomEase.min.js'), NULL, '1.0', true);
    wp_enqueue_script('ScrollTrigger', get_theme_file_uri('/libs/ScrollTrigger.min.js'), NULL, '1.0', true);
    wp_enqueue_script('main js', get_theme_file_uri('/assets/js/main.js'), NULL, '1.0', true);
    wp_enqueue_script('Header', get_theme_file_uri('/assets/js/header.js'), NULL, '1.0', true);

    // blocks
    wp_enqueue_script('big header block js', get_theme_file_uri('/blocks/js/block-big-header-block.js'), NULL, '1.0', true);
    wp_enqueue_script('two header column block js', get_theme_file_uri('/blocks/js/block-header-two-column-block.js'), NULL, '1.0', true);
    wp_enqueue_script('image textTitle js', get_theme_file_uri('/blocks/js/block-image-text-block.js'), NULL, '1.0', true);
    wp_enqueue_script('programma slider js', get_theme_file_uri('/blocks/js/block-programma-slider-block.js'), NULL, '1.0', true);
    wp_enqueue_script('sponsor slider js', get_theme_file_uri('/blocks/js/block-sponsor-slider-block.js'), NULL, '1.0', true);
    wp_enqueue_script('header checklist js', get_theme_file_uri('/blocks/js/block-header-checklist-block.js'), NULL, '1.0', true);
    wp_enqueue_script('faq overview js', get_theme_file_uri('/blocks/js/block-faq-overview-block.js'), NULL, '1.0', true);
    wp_enqueue_script('Image gallery block js', get_theme_file_uri('/blocks/js/block-image-gallery-block.js'), NULL, '1.0', true);

    wp_enqueue_style('main', get_stylesheet_uri());

  }

  add_action( 'wp_head', 'ilc_favicon');
  function ilc_favicon(){
      echo "<link rel='shortcut icon' href='" . get_stylesheet_directory_uri() . "/favicon.ico' />" . "\n";
  }

  // main information
  function jn_main_information($wp_customize){
    $wp_customize->add_section('kliekpop-main-callout-section', array(
      'title' => 'Main Information'
    ));
    $wp_customize->add_setting('kliekpop-main-callout-title');
    $wp_customize->add_control(new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-title-control', array(
      'label' => 'Title',
      'section' => 'kliekpop-main-callout-section',
      'settings' => 'kliekpop-main-callout-title'
    )));
    $wp_customize->add_setting('kliekpop-main-callout-description');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-description-control', array(
        'label' => 'Description',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-description',
        'type' => 'textarea'
    )));
    $wp_customize->add_setting('kliekpop-main-callout-featured-image');
    $wp_customize->add_control( new WP_Customize_Media_Control($wp_customize,
    'kliekpop-main-callout-featured-image-control', array(
        'label' => 'Image',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-featured-image',
        'width' => 750,
        'height' => 500
    )));
    $wp_customize->add_setting('kliekpop-main-callout-logo');
    $wp_customize->add_control( new WP_Customize_Media_Control($wp_customize,
    'kliekpop-main-callout-logo-control', array(
        'label' => 'logo',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-logo',
        'width' => 750,
        'height' => 500
    )));
    $wp_customize->add_setting('kliekpop-main-callout-logo-white');
    $wp_customize->add_control( new WP_Customize_Media_Control($wp_customize,
    'kliekpop-main-callout-logo-white-control', array(
        'label' => 'logo (white)',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-logo-white',
        'width' => 750,
        'height' => 500
    )));
    $wp_customize->add_setting('kliekpop-main-callout-telephone');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-telephone-control', array(
        'label' => 'Telephone',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-telephone',
    )));
    $wp_customize->add_setting('kliekpop-main-callout-mail');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-mail-control', array(
        'label' => 'Mail',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-mail',
    )));
    $wp_customize->add_setting('kliekpop-main-callout-address');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-address-control', array(
        'label' => 'Address',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-address',
    )));
    $wp_customize->add_setting('kliekpop-main-callout-tickets');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-tickets-control', array(
        'label' => 'Tickets URL',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-tickets',
    )));
    $wp_customize->add_setting('kliekpop-main-callout-facebook');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-facebook-control', array(
        'label' => 'Facebook URL',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-facebook',
    )));
    $wp_customize->add_setting('kliekpop-main-callout-instagram');
    $wp_customize->add_control( new WP_Customize_Control($wp_customize,
    'kliekpop-main-callout-instagram-control', array(
        'label' => 'Instagram URL',
        'section' => 'kliekpop-main-callout-section',
        'settings' => 'kliekpop-main-callout-instagram',
    )));
  }

  add_action('wp_enqueue_scripts', 'jn_files');
  add_action('customize_register', 'jn_main_information');

  // menu
  add_theme_support('menus');
  register_nav_menus(
    array(
      'primary-menu' => 'Primary Menu',
      'secondary-menu' => 'Secondary Menu',
      'primary-footer-menu' => 'Primary Footer Menu',
      'secondary-footer-menu' => 'Secondary Footer Menu',
      'third-footer-menu' => 'Third Footer Menu',
      'fourth-footer-menu' => 'Fourth Footer Menu',
      'fifth-footer-menu' => 'Fifth Footer Menu'
    )
  );

  remove_filter('wp_robots', 'wp_robots_max_image_preview_large');

?>
